# 仿真结果JSON格式更新文档

## 更新概述

仿真结果JSON的数据结构已进行重大更新，主要变化：
1. **层次结构调整**：原有四大指标重新组织到`network_metrics`下
2. **新增路段分析**：添加`selected_edge_metrics`用于用户自选路段的聚合指标
3. **数据结构优化**：移除冗余信息，使结构更清晰

---

## 1. 新的JSON结构

### 1.1 整体结构对比

**旧结构**：
```json
{
  "simulation_results": {
    "pedestrian_metrics": { ... },
    "vehicle_metrics": { ... },
    "vip_vehicle_metrics": { ... },
    "venue_area_metrics": { ... }
  }
}
```

**新结构**：
```json
{
  "simulation_results": {
    "network_metrics": {
      "pedestrian_metrics": { ... },
      "vehicle_metrics": { ... },
      "vip_vehicle_metrics": { ... },
      "venue_area_metrics": { ... }
    },
    "selected_edge_metrics": {
      "pedestrian_metrics": { ... },
      "vehicle_metrics": { ... }
    }
  }
}
```

### 1.2 完整示例

```json
{
  "simulation_results": {
    "network_metrics": {
      "pedestrian_metrics": {
        "avg_duration": 245.6,
        "avg_waitingTime": 18.7,
        "avg_waitingCount": 2.3,
        "avg_timeLoss": 45.2
      },
      "vehicle_metrics": {
        "avg_duration": 312.8,
        "avg_waiting_time": 67.4,
        "avg_waiting_count": 3.1,
        "avg_time_loss": 89.6
      },
      "vip_vehicle_metrics": {
        "vip_avg_duration": 198.3,
        "vip_avg_waiting_time": 12.5,
        "vip_avg_waiting_count": 1.2,
        "vip_avg_time_loss": 23.7
      },
      "venue_area_metrics": {
        "people_avg_traveltime": 78.9,
        "people_avg_timeloss": 23.4,
        "vehicle_avg_traveltime": 156.7,
        "vehicle_avg_waitingtime": 45.8,
        "vehicle_avg_timeloss": 67.3,
        "vehicle_avg_speed": 8.2,
        "vehicle_avg_occupancy": 0.34
      }
    },
    "selected_edge_metrics": {
      "pedestrian_metrics": {
        "total_departed": 150.0,
        "total_arrived": 148.0,
        "total_entered": 160.0,
        "avg_traveltime": 45.5,
        "avg_waitingTime": 12.3,
        "avg_speed": 8.2,
        "avg_timeLoss": 15.7,
        "avg_occupancy": 0.23
      },
      "vehicle_metrics": {
        "total_departed": 89.0,
        "total_arrived": 87.0,
        "total_entered": 95.0,
        "avg_traveltime": 78.9,
        "avg_waitingTime": 28.4,
        "avg_speed": 6.8,
        "avg_timeLoss": 45.6,
        "avg_occupancy": 1.45
      }
    }
  }
}
```

---

## 2. 数据字段详细说明

### 2.1 network_metrics（网络级指标）

这是原有的四大指标，现在归类到`network_metrics`下：

#### `pedestrian_metrics` - 行人指标
| 字段名 | 类型 | 单位 | 描述 |
|--------|------|------|------|
| `avg_duration` | number | 秒 | 平均持续时间 |
| `avg_waitingTime` | number | 秒 | 平均等待时间 |
| `avg_waitingCount` | number | 次 | 平均等待次数 |
| `avg_timeLoss` | number | 秒 | 平均时间损失 |

#### `vehicle_metrics` - 车辆指标
| 字段名 | 类型 | 单位 | 描述 |
|--------|------|------|------|
| `avg_duration` | number | 秒 | 平均行程时间 |
| `avg_waiting_time` | number | 秒 | 平均等待时间 |
| `avg_waiting_count` | number | 次 | 平均等待次数 |
| `avg_time_loss` | number | 秒 | 平均时间损失 |

#### `vip_vehicle_metrics` - 贵宾车辆指标
| 字段名 | 类型 | 单位 | 描述 |
|--------|------|------|------|
| `vip_avg_duration` | number | 秒 | VIP车辆平均行程时间 |
| `vip_avg_waiting_time` | number | 秒 | VIP车辆平均等待时间 |
| `vip_avg_waiting_count` | number | 次 | VIP车辆平均等待次数 |
| `vip_avg_time_loss` | number | 秒 | VIP车辆平均时间损失 |

#### `venue_area_metrics` - 场馆区域指标（仅离场场景）
| 字段名 | 类型 | 单位 | 描述 |
|--------|------|------|------|
| `people_avg_traveltime` | number | 秒 | 行人平均行程时间 |
| `people_avg_timeloss` | number | 秒 | 行人平均时间损失 |
| `vehicle_avg_traveltime` | number | 秒 | 车辆平均行程时间 |
| `vehicle_avg_waitingtime` | number | 秒 | 车辆平均等待时间 |
| `vehicle_avg_timeloss` | number | 秒 | 车辆平均时间损失 |
| `vehicle_avg_speed` | number | m/s | 车辆平均速度 |
| `vehicle_avg_occupancy` | number | - | 车辆平均占用率 |

### 2.2 selected_edge_metrics（路段指标）

**存在条件**：仅当配置中启用了路段分析时才存在此字段。

#### `pedestrian_metrics` / `vehicle_metrics`
路段指标的字段与网络指标不同，包含更详细的统计信息：

| 字段名 | 类型 | 单位 | 描述 |
|--------|------|------|------|
| `total_departed` | number | 人次/车次 | 总发车/出发数量 |
| `total_arrived` | number | 人次/车次 | 总到达数量 |
| `total_entered` | number | 人次/车次 | 总进入数量 |
| `avg_traveltime` | number | 秒 | 平均行程时间（按出发数量加权） |
| `avg_waitingTime` | number | 秒 | 平均等待时间（按出发数量加权） |
| `avg_speed` | number | m/s | 平均速度 |
| `avg_timeLoss` | number | 秒 | 平均时间损失（按出发数量加权） |
| `avg_occupancy` | number | - | 平均占用率 |

---

## 3. 前端代码适配指南

### 3.1 原有代码需要的修改

**修改前**：
```javascript
// 旧的数据访问方式
const pedMetrics = results.simulation_results.pedestrian_metrics;
const vehMetrics = results.simulation_results.vehicle_metrics;
const vipMetrics = results.simulation_results.vip_vehicle_metrics;
const venueMetrics = results.simulation_results.venue_area_metrics;
```

**修改后**：
```javascript
// 新的数据访问方式
const networkMetrics = results.simulation_results.network_metrics;
const pedMetrics = networkMetrics.pedestrian_metrics;
const vehMetrics = networkMetrics.vehicle_metrics;
const vipMetrics = networkMetrics.vip_vehicle_metrics;
const venueMetrics = networkMetrics.venue_area_metrics;

// 新增：路段分析结果（可选）
const edgeMetrics = results.simulation_results.selected_edge_metrics;
```

### 3.2 兼容性处理

为了确保代码的向后兼容性，建议添加检查：

```javascript
function getNetworkMetrics(results) {
    // 尝试新格式
    if (results.simulation_results.network_metrics) {
        return results.simulation_results.network_metrics;
    }
    
    // 回退到旧格式
    return {
        pedestrian_metrics: results.simulation_results.pedestrian_metrics,
        vehicle_metrics: results.simulation_results.vehicle_metrics,
        vip_vehicle_metrics: results.simulation_results.vip_vehicle_metrics,
        venue_area_metrics: results.simulation_results.venue_area_metrics
    };
}

function hasEdgeMetrics(results) {
    return !!results.simulation_results.selected_edge_metrics;
}
```

### 3.3 完整的结果显示函数

```javascript
function displaySimulationResults(results) {
    // 获取网络级指标
    const networkMetrics = getNetworkMetrics(results);
    
    // 显示行人指标
    displayPedestrianMetrics(networkMetrics.pedestrian_metrics);
    
    // 显示车辆指标
    displayVehicleMetrics(networkMetrics.vehicle_metrics);
    
    // 显示VIP车辆指标（如果存在）
    if (networkMetrics.vip_vehicle_metrics) {
        displayVipVehicleMetrics(networkMetrics.vip_vehicle_metrics);
    }
    
    // 显示场馆区域指标（如果存在）
    if (networkMetrics.venue_area_metrics) {
        displayVenueAreaMetrics(networkMetrics.venue_area_metrics);
    }
    
    // 显示路段分析结果（如果存在）
    if (hasEdgeMetrics(results)) {
        displayEdgeAnalysisResults(results.simulation_results.selected_edge_metrics);
    }
}

function displayPedestrianMetrics(metrics) {
    document.getElementById('pedAvgDuration').textContent = metrics.avg_duration.toFixed(1) + 's';
    document.getElementById('pedAvgWaitingTime').textContent = metrics.avg_waitingTime.toFixed(1) + 's';
    document.getElementById('pedAvgWaitingCount').textContent = metrics.avg_waitingCount.toFixed(1);
    document.getElementById('pedAvgTimeLoss').textContent = metrics.avg_timeLoss.toFixed(1) + 's';
}

function displayVehicleMetrics(metrics) {
    document.getElementById('vehAvgDuration').textContent = metrics.avg_duration.toFixed(1) + 's';
    document.getElementById('vehAvgWaitingTime').textContent = metrics.avg_waiting_time.toFixed(1) + 's';
    document.getElementById('vehAvgWaitingCount').textContent = metrics.avg_waiting_count.toFixed(1);
    document.getElementById('vehAvgTimeLoss').textContent = metrics.avg_time_loss.toFixed(1) + 's';
}

function displayVipVehicleMetrics(metrics) {
    document.getElementById('vipAvgDuration').textContent = metrics.vip_avg_duration.toFixed(1) + 's';
    document.getElementById('vipAvgWaitingTime').textContent = metrics.vip_avg_waiting_time.toFixed(1) + 's';
    document.getElementById('vipAvgWaitingCount').textContent = metrics.vip_avg_waiting_count.toFixed(1);
    document.getElementById('vipAvgTimeLoss').textContent = metrics.vip_avg_time_loss.toFixed(1) + 's';
}

function displayVenueAreaMetrics(metrics) {
    // 行人场馆指标
    document.getElementById('venuePedTravelTime').textContent = metrics.people_avg_traveltime.toFixed(1) + 's';
    document.getElementById('venuePedTimeLoss').textContent = metrics.people_avg_timeloss.toFixed(1) + 's';
    
    // 车辆场馆指标
    document.getElementById('venueVehTravelTime').textContent = metrics.vehicle_avg_traveltime.toFixed(1) + 's';
    document.getElementById('venueVehWaitingTime').textContent = metrics.vehicle_avg_waitingtime.toFixed(1) + 's';
    document.getElementById('venueVehTimeLoss').textContent = metrics.vehicle_avg_timeloss.toFixed(1) + 's';
    document.getElementById('venueVehSpeed').textContent = metrics.vehicle_avg_speed.toFixed(2) + 'm/s';
    document.getElementById('venueVehOccupancy').textContent = (metrics.vehicle_avg_occupancy * 100).toFixed(1) + '%';
}

function displayEdgeAnalysisResults(edgeMetrics) {
    const edgeResults = document.getElementById('edgeAnalysisResults');
    edgeResults.style.display = 'block';
    
    // 显示行人路段指标
    const pedMetrics = edgeMetrics.pedestrian_metrics;
    document.getElementById('edgePedDeparted').textContent = pedMetrics.total_departed;
    document.getElementById('edgePedArrived').textContent = pedMetrics.total_arrived;
    document.getElementById('edgePedEntered').textContent = pedMetrics.total_entered;
    document.getElementById('edgePedTravelTime').textContent = pedMetrics.avg_traveltime.toFixed(1) + 's';
    document.getElementById('edgePedWaitingTime').textContent = pedMetrics.avg_waitingTime.toFixed(1) + 's';
    document.getElementById('edgePedSpeed').textContent = pedMetrics.avg_speed.toFixed(2) + 'm/s';
    document.getElementById('edgePedTimeLoss').textContent = pedMetrics.avg_timeLoss.toFixed(1) + 's';
    document.getElementById('edgePedOccupancy').textContent = (pedMetrics.avg_occupancy * 100).toFixed(1) + '%';
    
    // 显示车辆路段指标
    const vehMetrics = edgeMetrics.vehicle_metrics;
    document.getElementById('edgeVehDeparted').textContent = vehMetrics.total_departed;
    document.getElementById('edgeVehArrived').textContent = vehMetrics.total_arrived;
    document.getElementById('edgeVehEntered').textContent = vehMetrics.total_entered;
    document.getElementById('edgeVehTravelTime').textContent = vehMetrics.avg_traveltime.toFixed(1) + 's';
    document.getElementById('edgeVehWaitingTime').textContent = vehMetrics.avg_waitingTime.toFixed(1) + 's';
    document.getElementById('edgeVehSpeed').textContent = vehMetrics.avg_speed.toFixed(2) + 'm/s';
    document.getElementById('edgeVehTimeLoss').textContent = vehMetrics.avg_timeLoss.toFixed(1) + 's';
    document.getElementById('edgeVehOccupancy').textContent = (vehMetrics.avg_occupancy * 100).toFixed(1) + '%';
}
```

---

## 4. HTML结构建议

### 4.1 网络级指标显示区域（已有，需小幅调整）

```html
<div class="results-section">
    <h3>网络级仿真指标</h3>
    
    <!-- 行人指标 -->
    <div class="metric-group">
        <h4>行人指标</h4>
        <div class="metric-item">
            <span class="label">平均持续时间：</span>
            <span id="pedAvgDuration" class="value">-</span>
        </div>
        <!-- 其他行人指标... -->
    </div>
    
    <!-- 车辆指标 -->
    <div class="metric-group">
        <h4>车辆指标</h4>
        <div class="metric-item">
            <span class="label">平均行程时间：</span>
            <span id="vehAvgDuration" class="value">-</span>
        </div>
        <!-- 其他车辆指标... -->
    </div>
    
    <!-- VIP车辆指标和场馆指标... -->
</div>
```

### 4.2 路段分析结果显示区域（新增）

```html
<div id="edgeAnalysisResults" class="results-section" style="display: none;">
    <h3>用户自选路段指标</h3>
    
    <div class="metrics-grid">
        <div class="metric-group">
            <h4>行人路段指标</h4>
            <div class="metric-item">
                <span class="label">总出发数：</span>
                <span id="edgePedDeparted" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">总到达数：</span>
                <span id="edgePedArrived" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">总进入数：</span>
                <span id="edgePedEntered" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均行程时间：</span>
                <span id="edgePedTravelTime" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均等待时间：</span>
                <span id="edgePedWaitingTime" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均速度：</span>
                <span id="edgePedSpeed" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均时间损失：</span>
                <span id="edgePedTimeLoss" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均占用率：</span>
                <span id="edgePedOccupancy" class="value">-</span>
            </div>
        </div>
        
        <div class="metric-group">
            <h4>车辆路段指标</h4>
            <div class="metric-item">
                <span class="label">总出发数：</span>
                <span id="edgeVehDeparted" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">总到达数：</span>
                <span id="edgeVehArrived" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">总进入数：</span>
                <span id="edgeVehEntered" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均行程时间：</span>
                <span id="edgeVehTravelTime" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均等待时间：</span>
                <span id="edgeVehWaitingTime" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均速度：</span>
                <span id="edgeVehSpeed" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均时间损失：</span>
                <span id="edgeVehTimeLoss" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均占用率：</span>
                <span id="edgeVehOccupancy" class="value">-</span>
            </div>
        </div>
    </div>
</div>
```

---

## 5. 关键更新要点

### 5.1 必须修改的代码位置
1. **数据访问路径**：所有访问原有四大指标的代码都需要加上`network_metrics.`前缀
2. **结果显示函数**：需要更新所有显示仿真结果的函数
3. **数据解析逻辑**：需要检查新的路段指标是否存在

### 5.2 向后兼容性
- 提供兼容性检查函数，支持旧格式数据
- 逐步迁移到新格式，避免一次性大幅修改

### 5.3 用户体验改进
- 路段分析结果只有在启用时才显示
- 提供清晰的指标分组和标签
- 数值格式化和单位显示

### 5.4 错误处理
- 检查指标数据是否存在再进行显示
- 为缺失的数据提供默认值或提示
- 处理网络请求异常情况

这个更新为前端提供了更清晰的数据结构，同时新增了强大的路段分析功能，提升了系统的分析能力。 