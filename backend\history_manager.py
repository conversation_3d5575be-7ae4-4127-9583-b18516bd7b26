import os
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

class HistoryManager:
    """历史方案管理器"""
    
    def __init__(self):
        self.history_dir = 'sumo_data/history'
        self.history_file = os.path.join(self.history_dir, 'saved_schemes.json')
        self._ensure_history_directory()
    
    def _ensure_history_directory(self):
        """确保历史目录存在"""
        os.makedirs(self.history_dir, exist_ok=True)
        
        # 如果历史文件不存在，创建空的历史记录
        if not os.path.exists(self.history_file):
            self._save_history_data([])
    
    def _load_history_data(self) -> List[Dict[str, Any]]:
        """加载历史数据"""
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_history_data(self, data: List[Dict[str, Any]]):
        """保存历史数据"""
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _generate_scheme_id(self) -> str:
        """生成方案ID"""
        return datetime.now().strftime("%y%m%d_%H%M%S")
    
    def save_scheme(self, config: Dict[str, Any], name: str = None, description: str = None) -> Dict[str, Any]:
        """
        保存配置方案
        
        参数:
        config: dict - 配置数据
        name: str - 方案名称（可选）
        description: str - 方案描述（可选）
        
        返回:
        dict - 保存结果
        """
        try:
            # 生成方案ID和时间戳
            scheme_id = self._generate_scheme_id()
            timestamp = datetime.now().isoformat()
            
            # 生成默认名称
            if not name:
                name = f"方案_{scheme_id}"
            
            # 创建方案记录
            scheme_record = {
                'id': scheme_id,
                'name': name,
                'description': description or '',
                'created_time': timestamp,
                'config': config,
                'config_summary': self._generate_config_summary(config)
            }
            
            # 加载现有历史数据
            history_data = self._load_history_data()
            
            # 添加新方案到列表开头（最新的在前面）
            history_data.insert(0, scheme_record)
            
            # 限制历史记录数量（最多保存50个）
            if len(history_data) > 50:
                history_data = history_data[:50]
            
            # 保存更新后的历史数据
            self._save_history_data(history_data)
            
            # 同时保存单独的配置文件
            scheme_file = os.path.join(self.history_dir, f'scheme_{scheme_id}.json')
            with open(scheme_file, 'w', encoding='utf-8') as f:
                json.dump(scheme_record, f, ensure_ascii=False, indent=2)
            
            return {
                'success': True,
                'scheme_id': scheme_id,
                'message': f'方案 "{name}" 保存成功',
                'scheme_file': scheme_file
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'保存方案失败: {str(e)}'
            }
    
    def get_schemes_list(self) -> List[Dict[str, Any]]:
        """获取历史方案列表"""
        history_data = self._load_history_data()
        
        # 返回简化的方案信息（不包含完整配置）
        schemes_list = []
        for scheme in history_data:
            schemes_list.append({
                'id': scheme['id'],
                'name': scheme['name'],
                'description': scheme['description'],
                'created_time': scheme['created_time'],
                'config_summary': scheme['config_summary']
            })
        
        return schemes_list
    
    def get_scheme_by_id(self, scheme_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取方案详情"""
        history_data = self._load_history_data()
        
        for scheme in history_data:
            if scheme['id'] == scheme_id:
                return scheme
        
        return None
    
    def delete_scheme(self, scheme_id: str) -> Dict[str, Any]:
        """删除方案"""
        try:
            history_data = self._load_history_data()
            
            # 查找并删除方案
            scheme_found = False
            updated_data = []
            for scheme in history_data:
                if scheme['id'] == scheme_id:
                    scheme_found = True
                else:
                    updated_data.append(scheme)
            
            if not scheme_found:
                return {
                    'success': False,
                    'error': f'未找到ID为 {scheme_id} 的方案'
                }
            
            # 保存更新后的数据
            self._save_history_data(updated_data)
            
            # 删除单独的配置文件
            scheme_file = os.path.join(self.history_dir, f'scheme_{scheme_id}.json')
            if os.path.exists(scheme_file):
                os.remove(scheme_file)
            
            return {
                'success': True,
                'message': f'方案 {scheme_id} 删除成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'删除方案失败: {str(e)}'
            }
    
    def update_scheme_info(self, scheme_id: str, name: str = None, description: str = None) -> Dict[str, Any]:
        """更新方案信息（名称和描述）"""
        try:
            history_data = self._load_history_data()
            
            # 查找并更新方案
            scheme_found = False
            for scheme in history_data:
                if scheme['id'] == scheme_id:
                    if name is not None:
                        scheme['name'] = name
                    if description is not None:
                        scheme['description'] = description
                    scheme_found = True
                    break
            
            if not scheme_found:
                return {
                    'success': False,
                    'error': f'未找到ID为 {scheme_id} 的方案'
                }
            
            # 保存更新后的数据
            self._save_history_data(history_data)
            
            return {
                'success': True,
                'message': f'方案 {scheme_id} 信息更新成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'更新方案信息失败: {str(e)}'
            }
    
    def _generate_config_summary(self, config: Dict[str, Any]) -> Dict[str, str]:
        """生成配置摘要"""
        try:
            # 网络配置摘要
            network_config = config.get('network_config', {})
            network_type = "预设路网" if network_config.get('type') == 'predefined' else "自定义路网"
            entrance_plan = network_config.get('entrance_plan', '未知')
            road_restriction = network_config.get('road_restriction', {})
            restriction_text = ""
            if road_restriction.get('enabled'):
                restricted_count = len(road_restriction.get('restricted_edges', []))
                restriction_text = f" + 道路限行({restricted_count}个路段)"
            
            network_summary = f"{network_type} - {entrance_plan}{restriction_text}"
            
            # 信号配置摘要
            signal_config = config.get('signal_config', {})
            signal_type = "预设配时" if signal_config.get('type') == 'predefined' else "自定义配时"
            optimization = signal_config.get('optimization', {})
            optimization_text = ""
            if optimization.get('enabled'):
                intersection_count = len(optimization.get('selected_intersections', []))
                optimization_text = f" + 自定义优化({intersection_count}个交叉口)"
            
            signal_summary = f"{signal_type}{optimization_text}"
            
            # 交通配置摘要
            traffic_config = config.get('traffic_config', {})
            traffic_type = "预设需求" if traffic_config.get('type') == 'predefined' else "自定义需求"
            scenario = traffic_config.get('scenario', '未知')
            vehicle_type = traffic_config.get('vehicle_type', '未知')
            vip_priority = traffic_config.get('vip_priority', {})
            vip_text = " + 贵宾专车" if vip_priority.get('enabled') else ""
            
            traffic_summary = f"{traffic_type} - {scenario}场景 + {vehicle_type}{vip_text}"
            
            return {
                'network': network_summary,
                'signal': signal_summary,
                'traffic': traffic_summary
            }
            
        except Exception as e:
            return {
                'network': '配置解析失败',
                'signal': '配置解析失败',
                'traffic': '配置解析失败'
            }
