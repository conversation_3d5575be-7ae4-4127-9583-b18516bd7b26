# 用户自选路段指标分析功能文档

## 功能概述

用户自选路段指标分析功能允许用户在仿真配置阶段选择特定的路段，系统会在仿真完成后自动计算这些路段的聚合指标，包括行人和车辆的通行效率数据。

**关键特性**：
- 在配置阶段选择路段，不是仿真后选择
- 支持所有仿真场景（进场、离场、自定义）
- 自动计算聚合指标，不分别计算每个路段
- 与网络级指标一起保存到结果JSON文件

---

## 1. 配置JSON格式更新

### 1.1 新增analysis_config部分

在原有的配置JSON中新增`analysis_config`模块：

```json
{
  "network_config": { ... },
  "signal_config": { ... },
  "traffic_config": { ... },
  "analysis_config": {
    "edge_analysis": {
      "enabled": true,
      "selected_edges": ["edge1", "edge2", "edge3"]
    }
  }
}
```

### 1.2 analysis_config字段说明

#### `edge_analysis` (可选)
- **类型**: `object`
- **描述**: 路段分析配置

##### `edge_analysis.enabled` (必填)
- **类型**: `boolean` 
- **描述**: 是否启用用户自选路段分析
- **默认值**: `false`
- **示例**: `true`

##### `edge_analysis.selected_edges` (条件必填)
- **类型**: `array[string]`
- **描述**: 用户选择的要分析的路段ID列表
- **适用条件**: 当 `enabled` 为 `true` 时必填
- **示例**: `["980398774#10", "-980398780#1", "1307189273#2"]`

### 1.3 完整配置示例

```json
{
  "network_config": {
    "type": "predefined",
    "file_path": null,
    "entrance_plan": "仅开放东侧出入口",
    "road_restriction": {
      "enabled": false,
      "restricted_edges": []
    }
  },
  "signal_config": {
    "type": "predefined",
    "file_path": null,
    "optimization": {
      "enabled": false,
      "selected_intersections": []
    }
  },
  "traffic_config": {
    "type": "predefined",
    "file_path": null,
    "scenario": "离场",
    "vehicle_type": "仅一般车辆",
    "vip_priority": {
      "enabled": false
    }
  },
  "analysis_config": {
    "edge_analysis": {
      "enabled": true,
      "selected_edges": [
        "980398774#10",
        "980398774#6", 
        "980398780#1",
        "-980398780#1"
      ]
    }
  }
}
```

---

## 2. 路段选择器调用方法

### 2.1 API接口

使用现有的网络选择器接口，但选择器类型为`edge`：

```http
POST /api/network/select
Content-Type: application/json
Timeout: 300秒
```

**请求体**：
```json
{
  "net_file_path": "sumo_data/templates/gym_tls.net.xml",
  "selector_type": "edge"
}
```

### 2.2 前端调用示例

```javascript
// 启动路段选择器（用于指标分析）
async function selectAnalysisEdges(netFilePath) {
    try {
        showLoading('正在启动路段选择器...');
        
        const response = await fetch('http://localhost:8888/api/network/select', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                net_file_path: netFilePath,
                selector_type: 'edge'
            }),
            timeout: 300000  // 5分钟超时，等待用户选择
        });
        
        const result = await response.json();
        hideLoading();
        
        if (result.success) {
            console.log(`用户选择了 ${result.count} 个分析路段:`, result.selected_ids);
            // 自动填写到配置表单的analysis_config部分
            updateAnalysisEdgeSelection(result.selected_ids);
            return result.selected_ids;
        } else {
            showError(result.error);
            return [];
        }
    } catch (error) {
        hideLoading();
        console.error('路段选择器启动失败:', error);
        return [];
    }
}

// 更新配置表单中的路段分析部分
function updateAnalysisEdgeSelection(selectedEdges) {
    // 启用路段分析
    document.getElementById('edgeAnalysisEnabled').checked = true;
    
    // 更新选择的路段列表显示
    const edgeListElement = document.getElementById('selectedEdgesList');
    edgeListElement.innerHTML = '';
    
    selectedEdges.forEach(edgeId => {
        const edgeItem = document.createElement('div');
        edgeItem.className = 'selected-edge-item';
        edgeItem.innerHTML = `
            <span class="edge-id">${edgeId}</span>
            <button type="button" onclick="removeEdge('${edgeId}')" class="remove-btn">×</button>
        `;
        edgeListElement.appendChild(edgeItem);
    });
    
    // 更新隐藏的配置数据
    updateConfigData();
}

// 从选择列表中移除路段
function removeEdge(edgeId) {
    const edgeItems = document.querySelectorAll('.selected-edge-item');
    edgeItems.forEach(item => {
        if (item.querySelector('.edge-id').textContent === edgeId) {
            item.remove();
        }
    });
    updateConfigData();
}

// 更新配置数据
function updateConfigData() {
    const enabled = document.getElementById('edgeAnalysisEnabled').checked;
    const selectedEdges = Array.from(document.querySelectorAll('.edge-id'))
                               .map(el => el.textContent);
    
    // 更新全局配置对象
    currentConfig.analysis_config = {
        edge_analysis: {
            enabled: enabled,
            selected_edges: selectedEdges
        }
    };
}
```

### 2.3 UI界面建议

在配置页面中添加路段分析配置区域：

```html
<!-- 路段指标分析配置 -->
<div class="config-section">
    <h3>路段指标分析</h3>
    
    <div class="form-group">
        <label>
            <input type="checkbox" id="edgeAnalysisEnabled" onchange="toggleEdgeAnalysis()">
            启用用户自选路段指标分析
        </label>
    </div>
    
    <div id="edgeAnalysisConfig" class="sub-config" style="display: none;">
        <div class="form-group">
            <label>选择分析路段：</label>
            <button type="button" onclick="openEdgeSelector()" class="selector-btn">
                🎯 启动路段选择器
            </button>
            <div class="help-text">
                点击按钮启动可视化选择器，在地图上点选要分析的路段
            </div>
        </div>
        
        <div class="form-group">
            <label>已选择的路段 (<span id="edgeCount">0</span>个)：</label>
            <div id="selectedEdgesList" class="selected-items-list">
                <!-- 动态填充选择的路段 -->
            </div>
        </div>
    </div>
</div>
```

对应的JavaScript函数：

```javascript
// 切换路段分析配置显示
function toggleEdgeAnalysis() {
    const enabled = document.getElementById('edgeAnalysisEnabled').checked;
    const configDiv = document.getElementById('edgeAnalysisConfig');
    
    if (enabled) {
        configDiv.style.display = 'block';
    } else {
        configDiv.style.display = 'none';
        // 清空选择
        document.getElementById('selectedEdgesList').innerHTML = '';
    }
    
    updateConfigData();
}

// 打开路段选择器
async function openEdgeSelector() {
    // 获取当前网络文件路径
    const netFilePath = getCurrentNetworkFilePath();
    
    if (!netFilePath) {
        showError('请先配置仿真路网');
        return;
    }
    
    // 启动路段选择器
    const selectedEdges = await selectAnalysisEdges(netFilePath);
    
    // 更新边数统计
    document.getElementById('edgeCount').textContent = selectedEdges.length;
}

// 获取当前网络文件路径
function getCurrentNetworkFilePath() {
    const networkType = document.querySelector('input[name="networkType"]:checked')?.value;
    
    if (networkType === 'predefined') {
        return 'sumo_data/templates/gym_tls.net.xml'; // 预设路网路径
    } else if (networkType === 'custom') {
        return document.getElementById('customNetworkFile').value;
    }
    
    return null;
}
```

---

## 3. 仿真结果数据格式更新

### 3.1 新的JSON结构

仿真结果文件`simulation_result_{sim_id}.json`的结构已更新：

```json
{
  "simulation_results": {
    "network_metrics": {
      "pedestrian_metrics": { ... },
      "vehicle_metrics": { ... },
      "vip_vehicle_metrics": { ... },
      "venue_area_metrics": { ... }
    },
    "selected_edge_metrics": {
      "pedestrian_metrics": {
        "total_departed": 150.0,
        "total_arrived": 148.0,
        "total_entered": 160.0,
        "avg_traveltime": 45.5,
        "avg_waitingTime": 12.3,
        "avg_speed": 8.2,
        "avg_timeLoss": 15.7,
        "avg_occupancy": 0.23
      },
      "vehicle_metrics": {
        "total_departed": 89.0,
        "total_arrived": 87.0,
        "total_entered": 95.0,
        "avg_traveltime": 78.9,
        "avg_waitingTime": 28.4,
        "avg_speed": 6.8,
        "avg_timeLoss": 45.6,
        "avg_occupancy": 1.45
      }
    }
  }
}
```

### 3.2 指标字段说明

#### `selected_edge_metrics` (条件存在)
- **存在条件**: 仅当配置中启用了路段分析时才存在
- **描述**: 用户选择的所有路段的聚合指标

#### `pedestrian_metrics` / `vehicle_metrics`
每种类型的指标包含以下字段：

| 字段名 | 类型 | 单位 | 描述 |
|--------|------|------|------|
| `total_departed` | number | 人次/车次 | 总发车/出发数量 |
| `total_arrived` | number | 人次/车次 | 总到达数量 |
| `total_entered` | number | 人次/车次 | 总进入数量 |
| `avg_traveltime` | number | 秒 | 平均行程时间（按出发数量加权） |
| `avg_waitingTime` | number | 秒 | 平均等待时间（按出发数量加权） |
| `avg_speed` | number | m/s | 平均速度 |
| `avg_timeLoss` | number | 秒 | 平均时间损失（按出发数量加权） |
| `avg_occupancy` | number | % | 平均占用率 |

### 3.3 前端显示示例

```javascript
// 解析和显示路段分析结果
function displayEdgeAnalysisResults(results) {
    const edgeMetrics = results.simulation_results.selected_edge_metrics;
    
    if (!edgeMetrics) {
        console.log('本次仿真未启用路段分析');
        return;
    }
    
    // 显示行人指标
    const pedMetrics = edgeMetrics.pedestrian_metrics;
    document.getElementById('edgePedDeparted').textContent = pedMetrics.total_departed;
    document.getElementById('edgePedArrived').textContent = pedMetrics.total_arrived;
    document.getElementById('edgePedTravelTime').textContent = pedMetrics.avg_traveltime.toFixed(1) + 's';
    document.getElementById('edgePedWaitingTime').textContent = pedMetrics.avg_waitingTime.toFixed(1) + 's';
    document.getElementById('edgePedSpeed').textContent = pedMetrics.avg_speed.toFixed(2) + 'm/s';
    
    // 显示车辆指标
    const vehMetrics = edgeMetrics.vehicle_metrics;
    document.getElementById('edgeVehDeparted').textContent = vehMetrics.total_departed;
    document.getElementById('edgeVehArrived').textContent = vehMetrics.total_arrived;
    document.getElementById('edgeVehTravelTime').textContent = vehMetrics.avg_traveltime.toFixed(1) + 's';
    document.getElementById('edgeVehWaitingTime').textContent = vehMetrics.avg_waitingTime.toFixed(1) + 's';
    document.getElementById('edgeVehSpeed').textContent = vehMetrics.avg_speed.toFixed(2) + 'm/s';
    
    // 显示路段分析结果区域
    document.getElementById('edgeAnalysisResults').style.display = 'block';
}
```

对应的HTML结构：

```html
<!-- 路段分析结果显示 -->
<div id="edgeAnalysisResults" class="results-section" style="display: none;">
    <h3>用户自选路段指标</h3>
    
    <div class="metrics-grid">
        <div class="metric-group">
            <h4>行人指标</h4>
            <div class="metric-item">
                <span class="label">总出发数：</span>
                <span id="edgePedDeparted" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">总到达数：</span>
                <span id="edgePedArrived" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均行程时间：</span>
                <span id="edgePedTravelTime" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均等待时间：</span>
                <span id="edgePedWaitingTime" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均速度：</span>
                <span id="edgePedSpeed" class="value">-</span>
            </div>
        </div>
        
        <div class="metric-group">
            <h4>车辆指标</h4>
            <div class="metric-item">
                <span class="label">总出发数：</span>
                <span id="edgeVehDeparted" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">总到达数：</span>
                <span id="edgeVehArrived" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均行程时间：</span>
                <span id="edgeVehTravelTime" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均等待时间：</span>
                <span id="edgeVehWaitingTime" class="value">-</span>
            </div>
            <div class="metric-item">
                <span class="label">平均速度：</span>
                <span id="edgeVehSpeed" class="value">-</span>
            </div>
        </div>
    </div>
</div>
```

---

## 4. 关键注意事项

### 4.1 使用流程
1. **配置阶段**：用户在配置仿真时选择要分析的路段
2. **仿真执行**：系统自动为选择的路段收集数据
3. **结果计算**：仿真完成后自动计算聚合指标
4. **结果显示**：前端解析JSON并显示路段分析结果

### 4.2 兼容性
- 如果配置中没有`analysis_config`或`edge_analysis.enabled`为`false`，仿真结果JSON中不会包含`selected_edge_metrics`
- 路段分析功能与所有仿真场景兼容（进场、离场、自定义）
- 路段分析功能与其他功能（道路限行、信号优化）可以同时使用

### 4.3 数据特点
- **聚合指标**：不是每个路段单独的指标，而是所有选择路段的汇总指标
- **加权平均**：时间相关指标（行程时间、等待时间、时间损失）按出发数量加权平均
- **简单平均**：速度和占用率为所有时间间隔的简单平均

### 4.4 错误处理
- 如果选择的路段在仿真中没有数据，对应指标为0
- 如果网络文件不存在，路段选择器会返回错误
- 建议在启动选择器前验证网络文件路径

---

## 5. 示例代码集成

### 5.1 完整的配置更新函数

```javascript
// 更新完整配置对象
function updateFullConfig() {
    const config = {
        network_config: getNetworkConfig(),
        signal_config: getSignalConfig(), 
        traffic_config: getTrafficConfig()
    };
    
    // 检查是否启用了路段分析
    const edgeAnalysisEnabled = document.getElementById('edgeAnalysisEnabled')?.checked;
    if (edgeAnalysisEnabled) {
        const selectedEdges = Array.from(document.querySelectorAll('.edge-id'))
                                   .map(el => el.textContent);
        
        config.analysis_config = {
            edge_analysis: {
                enabled: true,
                selected_edges: selectedEdges
            }
        };
    }
    
    return config;
}
```

### 5.2 配置验证更新

```javascript
// 验证路段分析配置
function validateAnalysisConfig(analysisConfig) {
    if (!analysisConfig?.edge_analysis) {
        return { valid: true }; // 可选配置，不存在也有效
    }
    
    const edgeAnalysis = analysisConfig.edge_analysis;
    
    if (edgeAnalysis.enabled === true) {
        if (!edgeAnalysis.selected_edges || edgeAnalysis.selected_edges.length === 0) {
            return {
                valid: false,
                error: '启用路段分析时必须选择至少一个路段'
            };
        }
    }
    
    return { valid: true };
}
```

这个文档为前端同事提供了完整的路段分析功能实现指南，包括配置格式、接口调用、UI设计和数据处理等各个方面。 