<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大型活动管理系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <!-- 移除内联样式，使用外部CSS文件 -->
    <style>
        .chart-container {
            height: 300px;
            min-height: 120px;
            width: 100%;
            margin-top: 8px;  /* 从10px减少到8px */
            background-color: rgba(5, 32, 46, 0.6);
            border-radius: 4px;
            padding: 8px;  /* 从10px减少到8px */
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 历史方案样式 */
        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;  /* 从10px减少到8px */
            border-bottom: 1px solid #e0e0e0;
            background-color: rgba(255, 255, 255, 0.05);
            margin-bottom: 6px;  /* 从8px减少到6px */
            border-radius: 4px;
        }

        .history-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .history-info {
            flex: 3;
            padding-right: 5px; /* 添加右侧内边距 */
        }

        .history-name {
            font-weight: bold;
            font-size: 14px;
            color: #ffffff;
        }

        .history-date {
            font-size: 12px;
            color: #aaaaaa;
            margin-top: 3px;
        }

        .history-actions {
            display: flex;
            gap: 5px;
        }

        .history-actions button {
            padding: 4px 8px; /* 减小按钮内边距 */
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 13px; /* 减小字体大小 */
            transition: background-color 0.2s;
        }

        .view-btn {
            background-color: #2196F3;
            color: white;
        }

        .view-btn:hover {
            background-color: #0b7dda;
        }

        .compare-btn {
            background-color: #4CAF50;
            color: white;
        }

        .compare-btn:hover {
            background-color: #46a049;
        }

        .delete-btn {
            background-color: #f44336;
            color: white;
        }

        .delete-btn:hover {
            background-color: #da190b;
        }

        /* 配置对比样式 */
        .config-comparison {
            display: flex;
            gap: 10px; /* 从15px减少到10px */
        }

        .current-config, .compare-config {
            flex: 1;
            padding: 8px; /* 从10px减少到8px */
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }

        .current-config h3, .compare-config h3 {
            margin-top: 0;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 11px;
        }

        .compare-config {
            border-left: 2px solid rgba(255, 99, 132, 0.8);
        }

        .loading-message, .empty-message {
            padding: 10px;
            text-align: center;
            color: #aaaaaa;
        }

        .history-controls {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .save-btn, .refresh-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .save-btn {
            background-color: #4CAF50;
            color: white;
        }

        .refresh-btn {
            background-color: #2196F3;
            color: white;
        }

        /* 方案对比按钮样式 */
        .result-actions {
            position: absolute;
            top: -7px;
            right: 10px;
        }

        .compare-action-btn {
            background-color: #4CAF50;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .compare-action-btn:hover {
            background-color: #46a049;
        }

        /* 修改父元素样式，使绝对定位按钮相对于它定位 */
        .result-header {
            position: relative;  /* 添加相对定位 */
            padding-bottom: 5px; /* 减小底部内边距 */
            margin-bottom: 5px; /* 减小底部外边距 */
        }

        /* 对比结果显示样式 */
        .comparison-header {
            margin-top: 15px;
            padding: 10px;
            background-color: rgba(76, 175, 80, 0.2);
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .comparison-title {
            font-size: 16px;
            font-weight: bold;
            color: #ffffff;
        }

        .close-comparison {
            background: none;
            border: none;
            color: #ffffff;
            font-size: 18px;
            cursor: pointer;
        }

        /* 组织方案布局优化 */
        .org-options-layout {
            /* 改为 flex 并允许换行，使元素在不同屏宽下更紧凑地排列 */
            display: flex;
            flex-wrap: wrap;
            gap: 8px;  /* 从12px减少到8px */
            margin-top: 8px;  /* 从10px减少到8px */
        }

        /* 每一列按照实际空间自动缩放，保持最小宽度，提升利用率 */
        .org-column {
            flex: 1 1 220px;      /* 最小 220px，剩余空间平均分配 */
            display: flex;
            flex-direction: column;
            gap: 6px;  /* 从8px减少到6px */
        }

        .org-group {
            padding: 8px;  /* 从10px减少到8px */
            background-color: rgba(255,255,255,0.05);
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            gap: 4px;  /* 从6px减少到4px */
        }

        .signal-group {
            margin-top: 4px;  /* 从5px减少到4px */
        }

        .signal-options {
            display: flex;
            align-items: center;
            gap: 8px;  /* 从10px减少到8px */
            flex-wrap: nowrap;   /* 不换行，保持同一行 */
        }

        .signal-options .upload-btn {
            margin-top: 0;       /* 垂直居中对齐 */
        }

        /* 修改卡片和结果卡片的内边距 */
        .card, .result-card {
            padding: 10px;  /* 如果在外部CSS文件中定义，这里添加覆盖样式 */
            margin-bottom: 8px; /* 减小结果卡片之间的间距 */
        }

        /* 减小组织方案布局的间距 */
        .org-options-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;  /* 从12px减少到8px */
            margin-top: 8px;  /* 从10px减少到8px */
        }

        /* 每一列按照实际空间自动缩放，保持最小宽度，提升利用率 */
        .org-column {
            flex: 1 1 220px;      /* 最小 220px，剩余空间平均分配 */
            display: flex;
            flex-direction: column;
            gap: 6px;  /* 从8px减少到6px */
        }

        .org-group {
            padding: 8px;  /* 从10px减少到8px */
            background-color: rgba(255,255,255,0.05);
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            gap: 4px;  /* 从6px减少到4px */
        }

        .signal-group {
            margin-top: 4px;  /* 从5px减少到4px */
        }

        .signal-options {
            display: flex;
            align-items: center;
            gap: 8px;  /* 从10px减少到8px */
            flex-wrap: nowrap;   /* 不换行，保持同一行 */
        }

        /* 减小按钮和表单元素的内边距 */
        .action-buttons {
            margin-top: 8px;  /* 如需添加 */
            gap: 6px;  /* 如需添加 */
        }

        .sub-option {
            padding: 3px 0;  /* 添加更紧凑的内边距 */
        }

        /* 减小模态框内容的内边距 */
        .modal-body {
            padding: 8px; /* 从10px减少到8px */
        }

        .form-group {
            margin-bottom: 6px; /* 从8px减少到6px */
        }

        /* 减小指标网格的间距 */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 6px;
            padding: 5px;
            margin-bottom: 5px;
        }

        .metric-item {
            padding: 6px;  /* 如果在外部CSS文件中定义，这里添加覆盖样式 */
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>大型活动管理系统</h1>
        </header>

        <!-- 修改为水平布局 -->
        <main style="display: flex; flex-direction: row; gap: 10px;">
            <div style="flex: 1; max-width: 300px;">
                <!-- 配置部分的内容 -->
                <section class="card" id="networkSection">
                    <h2>仿真路网（单选）</h2>
                    <div class="option-group">
                        <div class="option">
                            <input type="radio" id="net-preset" name="network" value="preset" checked>
                            <label for="net-preset">预设</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="net-custom" name="network" value="custom">
                            <label for="net-custom">自定义</label>
                        </div>
                        <button class="upload-btn" id="uploadNetBtn">
                            <i class="bi bi-upload"></i> 加载文件(*.net.xml)
                        </button>
                    </div>
                </section>

                <section class="card" id="trafficSection">
                    <h2>交通需求（单选）</h2>
                    <div class="option-group">
                        <div class="option">
                            <input type="radio" id="traffic-preset" name="traffic" value="preset" checked>
                            <label for="traffic-preset">预设</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="traffic-custom" name="traffic" value="custom">
                            <label for="traffic-custom">自定义</label>
                        </div>
                        <button class="upload-btn" id="uploadRouBtn">
                            <i class="bi bi-upload"></i> 加载文件(*.rou.xml)
                        </button>
                    </div>
                    <div class="sub-options">
                        <div class="vehicle-time-container">
                            <div class="sub-option">
                                <span class="bullet">•</span>
                                <label>车辆类型：</label>
                                <div class="select-wrapper">
                                    <select id="vehicleType">
                                        <option value="general">一般车辆</option>
                                        <option value="vip">贵宾专车</option>
                                    </select>
                                </div>
                            </div>
                            <div class="sub-option">
                                <span class="bullet">•</span>
                                <label>组织时段：</label>
                                <div class="select-wrapper">
                                    <select id="timePhase">
                                        <option value="entrance">进场</option>
                                        <option value="exit">离场</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="card" id="organizationSection">
                    <h2>组织方案</h2>
                    <div class="sub-options">
                        <div class="org-options-layout">
                            <!-- 第一列：道路相关选项 -->
                            <div class="org-column">
                                <div class="org-group">
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>出入口方案：</label>
                                        <div class="select-wrapper">
                                            <select id="entranceType">
                                                <option value="east">仅开放东侧出入口</option>
                                                <option value="south">仅开放南侧出入口</option>
                                                <option value="all">全部开放</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>道路限行：</label>
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="roadRestriction">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第二列：车辆和信号相关选项 -->
                            <div class="org-column">
                                <div class="org-group">
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>贵宾专车优先通行：</label>
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="vipPriority">
                                        </div>
                                    </div>
                                </div>

                                <div class="org-group signal-group">
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>信号配时：</label>
                                        <div class="signal-options">
                                            <div class="option-group compact">
                                                <div class="option">
                                                    <input type="radio" id="signal-preset" name="signal" value="preset" checked>
                                                    <label for="signal-preset">预设</label>
                                                </div>
                                                <div class="option">
                                                    <input type="radio" id="signal-custom" name="signal" value="custom">
                                                    <label for="signal-custom">自定义</label>
                                                </div>
                                            </div>
                                            <button class="upload-btn" id="uploadAddBtn">
                                                <i class="bi bi-upload"></i> 加载文件(*.add.xml)
                                            </button>

                                        </div>
                                    </div>
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>信控优化：</label>
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="signalOptimization">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="card" id="analysisSection">
                    <h2>分析配置</h2>
                    <div class="sub-options">
                        <div class="sub-option">
                            <span class="bullet">•</span>
                            <label>用户自选路段指标分析：</label>
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="edgeAnalysis">
                            </div>
                            <span id="edgeAnalysisStatus" style="display: none; color: #4caf50; margin-left: 5px; font-size: 0.9em;"></span>
                        </div>
                        <div id="edgeAnalysisDetails" class="edge-analysis-details" style="display: none; margin-left: 20px; margin-top: 10px;">
                            <div class="sub-option">
                                <button class="upload-btn" id="selectEdgesBtn">
                                    <i class="bi bi-geo-alt"></i> 选择分析路段
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="action-buttons">
                    <button class="run-btn">运行方案</button>
                    <button class="load-btn" id="loadResultBtn">
                        <i class="bi bi-file-earmark-text"></i> 加载已有结果
                    </button>
                    <button class="history-btn" id="historyBtn">
                        <i class="bi bi-clock-history"></i> 历史方案
                    </button>
                </div>
            </div>

            <div style="flex: 3; display: flex; flex-direction: column;">
                <!-- 添加仿真结果部分 -->
                <section class="result-card" id="simulationInfoConfig">
                    <div class="result-header">
                        <h2>仿真概况与配置</h2>
                        <div class="result-actions">
                            <button id="compareButton" class="compare-action-btn">
                                <i class="bi bi-bar-chart"></i> 方案对比
                            </button>
                        </div>
                    </div>
                    <div class="simulation-info" style="margin-bottom:6px !important;">
                        <div>
                            <span><strong>仿真ID:</strong> <span id="simId">250702174456</span></span>
                            <span style="margin-left:15px"><strong>开始时间:</strong> <span id="startTime">2025-07-02 17:44:56</span></span>
                        </div>
                    </div>
                    <div class="config-summary">
                        <div class="config-comparison">
                            <div class="current-config">
                                <h3>当前方案</h3>
                                <div class="config-item" id="networkConfig">
                                    <span><strong>路网配置:</strong> <span>预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)</span></span>
                                </div>
                                <div class="config-item" id="signalConfig">
                                    <span><strong>信号配置:</strong> <span>预设配时 + 自定义优化(2个交叉口)</span></span>
                                </div>
                                <div class="config-item" id="trafficConfig">
                                    <span><strong>交通需求:</strong> <span>预设需求 - 进场场景 + 贵宾专车</span></span>
                                </div>
                            </div>
                            <div class="compare-config" style="display: none;">
                                <h3>对比方案 <span id="compareSchemeId"></span></h3>
                                <div class="config-item" id="compareNetworkConfig">
                                    <span><strong>路网配置:</strong> <span></span></span>
                                </div>
                                <div class="config-item" id="compareSignalConfig">
                                    <span><strong>信号配置:</strong> <span></span></span>
                                </div>
                                <div class="config-item" id="compareTrafficConfig">
                                    <span><strong>交通需求:</strong> <span></span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <div style="display:flex; flex-wrap:wrap; gap:10px; justify-content:space-between;">
                    <section class="result-card" id="pedestrianMetrics" style="flex:1; min-width:23%;">
                        <div class="result-header">
                            <h2>行人指标</h2>
                        </div>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <div class="metric-value" id="pedTravelTime">123.45</div>
                                <div class="metric-label">平均行程时间 (秒)</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="pedWaitingTime">12.34</div>
                                <div class="metric-label">平均等待时间 (秒)</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="pedWaitingCount">2.1</div>
                                <div class="metric-label">平均等待次数</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="pedTimeLoss">23.45</div>
                                <div class="metric-label">平均时间损失 (秒)</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="pedChart"></canvas>
                        </div>
                    </section>

                    <section class="result-card" id="vehicleMetrics" style="flex:1; min-width:23%;">
                        <div class="result-header">
                            <h2>一般车辆指标</h2>
                        </div>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <div class="metric-value" id="vehTravelTime">234.56</div>
                                <div class="metric-label">平均行程时间 (秒)</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="vehWaitingTime">45.67</div>
                                <div class="metric-label">平均等待时间 (秒)</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="vehWaitingCount">3.2</div>
                                <div class="metric-label">平均等待次数</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="vehTimeLoss">67.89</div>
                                <div class="metric-label">平均时间损失 (秒)</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="vehChart"></canvas>
                        </div>
                    </section>

                    <section class="result-card" id="vipVehicleMetrics" style="flex:1; min-width:23%;">
                        <div class="result-header">
                            <h2>贵宾专车指标</h2>
                        </div>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <div class="metric-value" id="vipTravelTime">145.23</div>
                                <div class="metric-label">平均行程时间 (秒)</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="vipWaitingTime">8.91</div>
                                <div class="metric-label">平均等待时间 (秒)</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="vipWaitingCount">1.2</div>
                                <div class="metric-label">平均等待次数</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="vipTimeLoss">12.34</div>
                                <div class="metric-label">平均时间损失 (秒)</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="vipChart"></canvas>
                        </div>
                    </section>

                    <section class="result-card" id="venueAreaMetrics" style="flex:1; min-width:23%;">
                        <div class="result-header">
                            <h2>场馆区域指标</h2>
                        </div>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <div class="metric-value" id="venuePedTime">156.78</div>
                                <div class="metric-label">行人平均行程时间 (秒)</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="venuePedDelay">34.56</div>
                                <div class="metric-label">行人平均延误 (秒)</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="venueVehTime">267.89</div>
                                <div class="metric-label">车辆平均行程时间 (秒)</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="venueVehDelay">78.90</div>
                                <div class="metric-label">车辆平均延误 (秒)</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="venueChart"></canvas>
                        </div>
                    </section>
                </div>

                <button class="back-btn" onclick="window.location.href='index.html'">返回主页</button>

                <!-- 添加路段分析结果显示区域 -->
                <section class="result-card" id="edgeAnalysisSection" style="display: none; margin-top: 10px;">
                    <div class="result-header">
                        <h2>用户自选路段分析结果</h2>
                    </div>
                    <div style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: space-between;">
                        <div style="flex: 1; min-width: 48%;">
                            <h3 class="metrics-subtitle">行人路段指标</h3>
                            <div class="metrics-grid">
                                <div class="metric-item">
                                    <div class="metric-value" id="edgePedTravelTime">-</div>
                                    <div class="metric-label">平均行程时间 (秒)</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="edgePedWaitingTime">-</div>
                                    <div class="metric-label">平均等待时间 (秒)</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="edgePedTimeLoss">-</div>
                                    <div class="metric-label">平均时间损失 (秒)</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="edgePedSpeed">-</div>
                                    <div class="metric-label">平均速度 (m/s)</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="edgePedCount">-</div>
                                    <div class="metric-label">总进入数 (人次)</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="edgePedOccupancy">-</div>
                                    <div class="metric-label">平均占用率 (%)</div>
                                </div>
                            </div>
                        </div>
                        <div style="flex: 1; min-width: 48%;">
                            <h3 class="metrics-subtitle">车辆路段指标</h3>
                            <div class="metrics-grid">
                                <div class="metric-item">
                                    <div class="metric-value" id="edgeVehTravelTime">-</div>
                                    <div class="metric-label">平均行程时间 (秒)</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="edgeVehWaitingTime">-</div>
                                    <div class="metric-label">平均等待时间 (秒)</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="edgeVehTimeLoss">-</div>
                                    <div class="metric-label">平均时间损失 (秒)</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="edgeVehSpeed">-</div>
                                    <div class="metric-label">平均速度 (m/s)</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="edgeVehCount">-</div>
                                    <div class="metric-label">总进入数 (车次)</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="edgeVehOccupancy">-</div>
                                    <div class="metric-label">平均占用率 (%)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- 历史方案模态框 -->
    <div id="historyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>历史方案管理</h3>
                <span class="close" id="closeHistoryModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="history-controls">
                    <button id="saveCurrentBtn" class="save-btn">
                        <i class="bi bi-floppy"></i> 保存当前配置
                    </button>
                    <button id="refreshHistoryBtn" class="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i> 刷新列表
                    </button>
                </div>

                <div class="history-list" id="historyList">
                    <div class="loading-message">正在加载历史方案...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 保存方案模态框 -->
    <div id="saveSchemeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>保存配置方案</h3>
                <span class="close" id="closeSaveModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="schemeName">方案名称：</label>
                    <input type="text" id="schemeName" placeholder="请输入方案名称（可选）">
                </div>
                <div class="form-group">
                    <label for="schemeDescription">方案描述：</label>
                    <textarea id="schemeDescription" placeholder="请输入方案描述（可选）" rows="3"></textarea>
                </div>
                <div class="config-preview">
                    <h4>配置预览：</h4>
                    <div id="configPreview" class="config-summary"></div>
                </div>
                <div class="modal-actions">
                    <button id="confirmSaveBtn" class="confirm-btn">确认保存</button>
                    <button id="cancelSaveBtn" class="cancel-btn">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 构建配置JSON - 供script.js中的startSimulation函数使用
            window.buildConfigJSON = function() {
                // 获取路网配置
                const networkType = document.querySelector('input[name="network"]:checked').value;
                const entranceType = document.getElementById('entranceType').value;
                const roadRestriction = document.getElementById('roadRestriction').checked;

                // 获取信号配置
                const signalType = document.querySelector('input[name="signal"]:checked').value;
                const signalOptimization = document.getElementById('signalOptimization').checked;

                // 获取交通需求配置
                const trafficType = document.querySelector('input[name="traffic"]:checked').value;
                const vehicleType = document.getElementById('vehicleType').value;
                const timePhase = document.getElementById('timePhase').value;
                const vipPriority = document.getElementById('vipPriority').checked;


                // 获取分析配置
                const edgeAnalysis = document.getElementById('edgeAnalysis').checked;

                // 构建完整配置对象
                return {
                    "network_config": {
                        "type": networkType === "preset" ? "predefined" : "custom",
                        "file_path": networkType === "custom" ? "custom_network.net.xml" : null,
                        "entrance_plan": entranceTypeToText(entranceType),
                        "road_restriction": {
                            "enabled": roadRestriction,
                            "restricted_edges": roadRestriction ? getRestrictedEdges() : []
                        }
                    },
                    "signal_config": {
                        "type": signalType === "preset" ? "predefined" : "custom",
                        "file_path": signalType === "custom" ? "custom_signal.add.xml" : null,
                        "optimization": {
                            "enabled": signalOptimization,
                            "selected_intersections": signalOptimization ? getSelectedIntersections() : []
                        }
                    },
                    "traffic_config": {
                        "type": trafficType === "preset" ? "predefined" : "custom",
                        "file_path": trafficType === "custom" ? "custom_traffic.rou.xml" : null,
                        "scenario": timePhaseToText(timePhase),
                        "vehicle_type": vehicleTypeToText(vehicleType),
                        "vip_priority": {
                            "enabled": vipPriority
                        }
                    },
                    "analysis_config": {
                        "edge_analysis": {
                            "enabled": edgeAnalysis,
                            "selected_edges": edgeAnalysis ? getSelectedAnalysisEdges() : []
                        }
                    }
                };
            }

            // 辅助函数，转换出入口选择为文本
            function entranceTypeToText(type) {
                switch(type) {
                    case "east": return "仅开放东侧出入口";
                    case "south": return "仅开放南侧出入口";
                    case "all": return "全部开放";
                    default: return "仅开放东侧出入口";
                }
            }

            // 辅助函数，转换车辆类型选择为文本
            function vehicleTypeToText(type) {
                switch(type) {
                    case "general": return "仅一般车辆";
                    case "vip": return "存在贵宾专车";
                    default: return "仅一般车辆";
                }
            }

            // 辅助函数，转换时段选择为文本
            function timePhaseToText(phase) {
                switch(phase) {
                    case "entrance": return "进场";
                    case "exit": return "离场";
                    default: return "进场";
                }
            }

            // 获取已选择的交叉口列表（从全局状态获取）
            function getSelectedIntersections() {
                // 从script.js中的全局变量获取
                return window.selectedIntersections || [];
            }

            // 获取已选择的限行路段列表（从全局状态获取）
            function getRestrictedEdges() {
                // 从script.js中的全局变量获取
                return window.selectedRestrictedEdges || [];
            }
            // 获取已选择的分析路段列表（从全局状态获取）
            function getSelectedAnalysisEdges() {
                // 从script.js中的全局变量获取
                return window.selectedAnalysisEdges || [];
            }

            // 构建结果数据，适配新的JSON格式
            window.navigateToResultPage = function(data) {
                // 构建结果数据，匹配results.html期望的格式
                const resultData = {
                    "simulation_id": data.simulation_id,
                    "config_summary": data.config_summary,
                    "start_time": new Date().toISOString(),  // 使用当前时间
                    "simulation_results": data.metrics  // 直接使用后端返回的新格式metrics
                };

                // 将数据编码为URL参数
                const encodedData = encodeURIComponent(JSON.stringify(resultData));

                // 跳转到结果页面
                window.location.href = `results.html?data=${encodedData}`;
            }

            // 显示错误信息 - 供script.js使用
            window.showError = function(message) {
                alert(`错误: ${message}`);
            }

            // 添加窗口大小变化时重新绘制图表的监听器
            window.addEventListener('resize', function() {
                // 如果已经存在图表实例，则在窗口调整大小后重新渲染
                if (window.pedestrianChart instanceof Chart) {
                    window.pedestrianChart.resize();
                }
                if (window.vehicleChart instanceof Chart) {
                    window.vehicleChart.resize();
                }
                if (window.vipVehicleChart instanceof Chart) {
                    window.vipVehicleChart.resize();
                }
                if (window.venueChart instanceof Chart) {
                    window.venueChart.resize();
                }
            });

            // 只有当页面包含结果数据参数时才加载和显示结果
            if (window.location.search.includes('data=')) {
                loadResultData().then(updateDisplay);
            } else {
                // 初始化默认图表
                window.addEventListener('load', function() {
                    console.log("初始化默认图表...");
                    // 等待DOM渲染完成，确保canvas元素尺寸已设置
                    setTimeout(function() {
                        const defaultMetrics = {
                            average_travel_time: 100,
                            average_waiting_time: 50,
                            average_waiting_count: 3,
                            average_time_loss: 70
                        };

                        console.log("正在初始化图表...");
                        if (document.getElementById('pedChart')) createPedestrianChart(defaultMetrics);
                        if (document.getElementById('vehChart')) createVehicleChart(defaultMetrics);
                        if (document.getElementById('vipChart')) createVIPVehicleChart(defaultMetrics);
                        if (document.getElementById('venueChart')) {
                            createVenueChart({
                                average_pedestrian_travel_time: 150,
                                average_pedestrian_delay: 30,
                                average_vehicle_travel_time: 250,
                                average_vehicle_delay: 80
                            });
                        }
                    }, 500); // 延迟500毫秒确保DOM完全渲染
                });
            }

            // 添加方案对比功能
            document.getElementById('historyBtn').addEventListener('click', function() {
                document.getElementById('historyModal').style.display = 'block';
                loadHistorySchemes();
            });

            // 直接方案对比按钮
            document.getElementById('compareButton').addEventListener('click', function() {
                document.getElementById('historyModal').style.display = 'block';
                loadHistorySchemes();
                // 显示提示
                alert("请从历史方案中选择一个方案进行对比");
            });

            // 关闭历史方案对话框
            document.getElementById('closeHistoryModal').addEventListener('click', function() {
                document.getElementById('historyModal').style.display = 'none';
            });

            // 加载历史方案列表
            function loadHistorySchemes() {
                const historyList = document.getElementById('historyList');
                historyList.innerHTML = '<div class="loading-message">正在加载历史方案...</div>';

                // 模拟获取历史方案数据
                setTimeout(function() {
                    const sampleSchemes = [
                        {id: "250702174456", name: "方案A - 东侧出入口", date: "2025-07-02 17:44:56"},
                        {id: "250702185532", name: "方案B - 南侧出入口", date: "2025-07-02 18:55:32"},
                        {id: "250703093022", name: "方案C - 全部开放", date: "2025-07-03 09:30:22"}
                    ];

                    if (sampleSchemes.length === 0) {
                        historyList.innerHTML = '<div class="empty-message">暂无历史方案</div>';
                        return;
                    }

                    let html = '';
                    sampleSchemes.forEach(scheme => {
                        html += `
                        <div class="history-item">
                            <div class="history-info">
                                <div class="history-name">${scheme.name}</div>
                                <div class="history-date">${scheme.date}</div>
                            </div>
                            <div class="history-actions">
                                <button class="view-btn" data-id="${scheme.id}">查看</button>
                                <button class="compare-btn" data-id="${scheme.id}">对比</button>
                                <button class="delete-btn" data-id="${scheme.id}">删除</button>
                            </div>
                        </div>`;
                    });

                    historyList.innerHTML = html;

                    // 添加按钮事件监听
                    document.querySelectorAll('.view-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const schemeId = this.getAttribute('data-id');
                            viewScheme(schemeId);
                        });
                    });

                    document.querySelectorAll('.compare-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const schemeId = this.getAttribute('data-id');
                            compareScheme(schemeId);
                        });
                    });

                    document.querySelectorAll('.delete-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const schemeId = this.getAttribute('data-id');
                            deleteScheme(schemeId);
                        });
                    });
                }, 1000);
            }

            // 查看方案详情
            function viewScheme(schemeId) {
                console.log("查看方案:", schemeId);
                // 模拟加载方案数据并显示
                loadSchemeData(schemeId).then(data => {
                    updateDisplay(data);
                    document.getElementById('historyModal').style.display = 'none';
                });
            }

            // 对比方案
            function compareScheme(schemeId) {
                console.log("对比方案:", schemeId);
                // 加载当前方案和选定方案的数据
                Promise.all([
                    loadResultData(),
                    loadSchemeData(schemeId)
                ]).then(([currentData, compareData]) => {
                    showComparison(currentData, compareData);
                    document.getElementById('historyModal').style.display = 'none';
                });
            }

            // 删除方案
            function deleteScheme(schemeId) {
                if (confirm(`确定要删除方案 ${schemeId} 吗？`)) {
                    console.log("删除方案:", schemeId);
                    // 模拟删除操作
                    alert("方案已删除");
                    loadHistorySchemes(); // 重新加载列表
                }
            }

            // 加载指定方案数据
            async function loadSchemeData(schemeId) {
                console.log("加载方案数据:", schemeId);
                // 模拟从服务器获取方案数据
                return new Promise(resolve => {
                    setTimeout(() => {
                        // 返回一个示例数据
                        const data = getDefaultResultData();
                        data.simulation_id = schemeId;

                        // 根据不同ID返回不同数据
                        if (schemeId === "250702185532") {
                            data.config_summary.network = "预设路网 - 仅开放南侧出入口 + 道路限行(3个路段)";
                            data.simulation_results.vehicle_metrics.average_travel_time = 210.34;
                            data.simulation_results.vehicle_metrics.average_waiting_time = 35.21;
                        } else if (schemeId === "250703093022") {
                            data.config_summary.network = "预设路网 - 全部开放 + 无道路限行";
                            data.simulation_results.vehicle_metrics.average_travel_time = 180.12;
                            data.simulation_results.vehicle_metrics.average_waiting_time = 25.67;
                        }

                        resolve(data);
                    }, 1000);
                });
            }

            // 显示方案对比
            function showComparison(currentData, compareData) {
                // 添加对比标题
                addComparisonHeader(compareData.simulation_id);

                // 创建对比图表
                createComparisonCharts(currentData, compareData);

                // 显示对比方案的配置
                displayComparisonConfig(compareData);
            }

            // 显示对比方案的配置信息
            function displayComparisonConfig(compareData) {
                // 显示对比方案的ID
                document.getElementById('compareSchemeId').textContent = `(${compareData.simulation_id})`;

                // 显示对比方案的配置信息
                document.querySelector('#compareNetworkConfig span span').textContent = compareData.config_summary.network;
                document.querySelector('#compareSignalConfig span span').textContent = compareData.config_summary.signal;
                document.querySelector('#compareTrafficConfig span span').textContent = compareData.config_summary.traffic;

                // 显示对比配置区域
                document.querySelector('.compare-config').style.display = 'block';
            }

            // 添加对比标题
            function addComparisonHeader(compareId) {
                // 检查是否已存在对比标题
                let header = document.querySelector('.comparison-header');
                if (!header) {
                    header = document.createElement('div');
                    header.className = 'comparison-header';
                    header.innerHTML = `
                        <div class="comparison-title">正在与方案 ${compareId} 进行对比</div>
                        <button class="close-comparison">&times;</button>
                    `;

                    // 在仿真概况之后插入
                    const simulationInfo = document.getElementById('simulationInfoConfig');
                    simulationInfo.parentNode.insertBefore(header, simulationInfo.nextSibling);

                    // 添加关闭对比的事件
                    header.querySelector('.close-comparison').addEventListener('click', function() {
                        // 移除对比标题
                        header.remove();

                        // 隐藏对比配置
                        document.querySelector('.compare-config').style.display = 'none';

                        // 恢复原始图表
                        loadResultData().then(updateDisplay);
                    });
                } else {
                    // 更新对比标题
                    header.querySelector('.comparison-title').textContent = `正在与方案 ${compareId} 进行对比`;
                }
            }

            // 创建对比图表
            function createComparisonCharts(currentData, compareData) {
                // 对比车辆指标
                createComparisonVehicleChart(
                    currentData.simulation_results.vehicle_metrics,
                    compareData.simulation_results.vehicle_metrics,
                    compareData.simulation_id
                );

                // 对比行人指标
                createComparisonPedestrianChart(
                    currentData.simulation_results.pedestrian_metrics,
                    compareData.simulation_results.pedestrian_metrics,
                    compareData.simulation_id
                );

                // 对比贵宾车辆指标
                createComparisonVIPChart(
                    currentData.simulation_results.vip_vehicle_metrics,
                    compareData.simulation_results.vip_vehicle_metrics,
                    compareData.simulation_id
                );

                // 对比场馆区域指标
                createComparisonVenueChart(
                    currentData.simulation_results.venue_area_metrics,
                    compareData.simulation_results.venue_area_metrics,
                    compareData.simulation_id
                );
            }

            // 创建车辆指标对比图
            function createComparisonVehicleChart(currentMetrics, compareMetrics, compareId) {
                const ctx = document.getElementById('vehChart').getContext('2d');

                // 销毁已存在的图表实例
                if (window.vehicleChart instanceof Chart) {
                    window.vehicleChart.destroy();
                }

                window.vehicleChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [
                            {
                                label: '当前方案',
                                data: [
                                    currentMetrics.average_travel_time,
                                    currentMetrics.average_waiting_time,
                                    currentMetrics.average_waiting_count,
                                    currentMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: `对比方案 (${compareId})`,
                                data: [
                                    compareMetrics.average_travel_time,
                                    compareMetrics.average_waiting_time,
                                    compareMetrics.average_waiting_count,
                                    compareMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: { color: '#ffffff' }
                            },
                            title: {
                                display: true,
                                text: '一般车辆指标对比',
                                color: '#ffffff'
                            }
                        }
                    }
                });
            }

            // 创建行人指标对比图
            function createComparisonPedestrianChart(currentMetrics, compareMetrics, compareId) {
                const ctx = document.getElementById('pedChart').getContext('2d');

                // 销毁已存在的图表实例
                if (window.pedestrianChart instanceof Chart) {
                    window.pedestrianChart.destroy();
                }

                window.pedestrianChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [
                            {
                                label: '当前方案',
                                data: [
                                    currentMetrics.average_travel_time,
                                    currentMetrics.average_waiting_time,
                                    currentMetrics.average_waiting_count,
                                    currentMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: `对比方案 (${compareId})`,
                                data: [
                                    compareMetrics.average_travel_time,
                                    compareMetrics.average_waiting_time,
                                    compareMetrics.average_waiting_count,
                                    compareMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: { color: '#ffffff' }
                            },
                            title: {
                                display: true,
                                text: '行人指标对比',
                                color: '#ffffff'
                            }
                        }
                    }
                });
            }

            // 创建贵宾车辆指标对比图
            function createComparisonVIPChart(currentMetrics, compareMetrics, compareId) {
                const ctx = document.getElementById('vipChart').getContext('2d');

                // 销毁已存在的图表实例
                if (window.vipVehicleChart instanceof Chart) {
                    window.vipVehicleChart.destroy();
                }

                window.vipVehicleChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [
                            {
                                label: '当前方案',
                                data: [
                                    currentMetrics.average_travel_time,
                                    currentMetrics.average_waiting_time,
                                    currentMetrics.average_waiting_count,
                                    currentMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: `对比方案 (${compareId})`,
                                data: [
                                    compareMetrics.average_travel_time,
                                    compareMetrics.average_waiting_time,
                                    compareMetrics.average_waiting_count,
                                    compareMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: { color: '#ffffff' }
                            },
                            title: {
                                display: true,
                                text: '贵宾专车指标对比',
                                color: '#ffffff'
                            }
                        }
                    }
                });
            }

            // 创建场馆区域指标对比图
            function createComparisonVenueChart(currentMetrics, compareMetrics, compareId) {
                const ctx = document.getElementById('venueChart').getContext('2d');

                // 销毁已存在的图表实例
                if (window.venueChart instanceof Chart) {
                    window.venueChart.destroy();
                }

                window.venueChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['行人平均行程时间', '行人平均延误', '车辆平均行程时间', '车辆平均延误'],
                        datasets: [
                            {
                                label: '当前方案',
                                data: [
                                    currentMetrics.average_pedestrian_travel_time,
                                    currentMetrics.average_pedestrian_delay,
                                    currentMetrics.average_vehicle_travel_time,
                                    currentMetrics.average_vehicle_delay
                                ],
                                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: `对比方案 (${compareId})`,
                                data: [
                                    compareMetrics.average_pedestrian_travel_time,
                                    compareMetrics.average_pedestrian_delay,
                                    compareMetrics.average_vehicle_travel_time,
                                    compareMetrics.average_vehicle_delay
                                ],
                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: { color: '#ffffff' }
                            },
                            title: {
                                display: true,
                                text: '场馆区域指标对比',
                                color: '#ffffff'
                            }
                        }
                    }
                });
            }

            // 确保在DOM完全加载后初始化图表
            document.addEventListener('DOMContentLoaded', function() {
                // 这个函数在DOMContentLoaded事件内部，确保DOM元素已加载
                console.log("DOM加载完成，准备初始化图表");

                // 如果图表容器存在但没有数据，可以先用默认数据初始化
                if (document.getElementById('pedChart') &&
                    document.getElementById('vehChart') &&
                    document.getElementById('vipChart') &&
                    document.getElementById('venueChart')) {

                    // 检查是否已经有数据加载
                    if (!window.resultDataLoaded) {
                        console.log("初始化图表...");
                        // 使用默认数据初始化图表
                        const defaultMetrics = {
                            average_travel_time: 0,
                            average_waiting_time: 0,
                            average_waiting_count: 0,
                            average_time_loss: 0
                        };

                        createPedestrianChart(defaultMetrics);
                        createVehicleChart(defaultMetrics);
                        createVIPVehicleChart(defaultMetrics);
                        createVenueChart(defaultMetrics);
                    }
                }
            });

            // 函数：用于从URL参数或JSON文件加载数据
            async function loadResultData() {
                try {
                    // 尝试从URL参数获取JSON
                    const urlParams = new URLSearchParams(window.location.search);
                    const jsonData = urlParams.get('data');

                    if (jsonData) {
                        return JSON.parse(decodeURIComponent(jsonData));
                    }

                    // 尝试从文件加载JSON（实际应用可能需要更复杂的逻辑）
                    const urlParams2 = new URLSearchParams(window.location.search);
                    const fileParam = urlParams2.get('file');

                    if (fileParam) {
                        const response = await fetch(fileParam);
                        if (response.ok) {
                            return await response.json();
                        }
                    }

                    // 如果没有获取到数据，返回默认数据
                    return getDefaultResultData();
                } catch (error) {
                    console.error("加载数据失败:", error);
                    return getDefaultResultData(); // 出错时使用默认数据
                }
            }

            // 获取默认数据
            function getDefaultResultData() {
                return {
                    simulation_id: "250702174456",
                    start_time: new Date().toISOString(),
                    config_summary: {
                        network: "预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)",
                        signal: "预设配时 + 自定义优化(2个交叉口)",
                        traffic: "预设需求 - 进场场景 + 贵宾专车"
                    },
                    simulation_results: {
                        pedestrian_metrics: {
                            average_travel_time: 123.45,
                            average_waiting_time: 12.34,
                            average_waiting_count: 2.1,
                            average_time_loss: 23.45
                        },
                        vehicle_metrics: {
                            average_travel_time: 234.56,
                            average_waiting_time: 45.67,
                            average_waiting_count: 3.2,
                            average_time_loss: 67.89
                        },
                        vip_vehicle_metrics: {
                            average_travel_time: 145.23,
                            average_waiting_time: 8.91,
                            average_waiting_count: 1.2,
                            average_time_loss: 12.34
                        },
                        venue_area_metrics: {
                            average_pedestrian_travel_time: 156.78,
                            average_pedestrian_delay: 34.56,
                            average_vehicle_travel_time: 267.89,
                            average_vehicle_delay: 78.90
                        }
                    }
                };
            }

            // 函数：更新页面显示
            function updateDisplay(data) {
                // 添加数据加载标记
                window.resultDataLoaded = true;

                // 更新仿真信息
                document.getElementById('simId').textContent = data.simulation_id;

                // 格式化并显示时间
                const startTimeRaw = new Date(data.start_time);
                const formattedTime = startTimeRaw.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                }).replace(/\//g, '-');
                document.getElementById('startTime').textContent = formattedTime;

                // 更新配置摘要
                if (data.config_summary) {
                    const networkElement = document.querySelector('#networkConfig span');
                    const signalElement = document.querySelector('#signalConfig span');
                    const trafficElement = document.querySelector('#trafficConfig span');

                    if (networkElement) networkElement.textContent = data.config_summary.network || '未知';
                    if (signalElement) signalElement.textContent = data.config_summary.signal || '未知';
                    if (trafficElement) trafficElement.textContent = data.config_summary.traffic || '未知';
                }

                // 获取指标数据
                const results = data.simulation_results;

                // 兼容性处理：检查新旧格式
                let networkMetrics;
                if (results.network_metrics) {
                    // 新格式
                    networkMetrics = results.network_metrics;
                } else {
                    // 旧格式，构造兼容结构
                    networkMetrics = {
                        pedestrian_metrics: results.pedestrian_metrics || {},
                        vehicle_metrics: results.vehicle_metrics || {},
                        vip_vehicle_metrics: results.vip_vehicle_metrics || {},
                        venue_area_metrics: results.venue_area_metrics || {}
                    };
                }

                // 安全更新指标的辅助函数
                function safeUpdateMetric(elementId, value, defaultValue = '-', decimals = 2) {
                    const element = document.getElementById(elementId);
                    if (element) {
                        if (value !== undefined && value !== null && !isNaN(value)) {
                            element.textContent = Number(value).toFixed(decimals);
                        } else {
                            element.textContent = defaultValue;
                        }
                    }
                }

                // 更新行人指标
                const pedMetrics = networkMetrics.pedestrian_metrics || {};
                safeUpdateMetric('pedTravelTime', pedMetrics.average_travel_time);
                safeUpdateMetric('pedWaitingTime', pedMetrics.average_waiting_time);
                safeUpdateMetric('pedWaitingCount', pedMetrics.average_waiting_count, '-', 1);
                safeUpdateMetric('pedTimeLoss', pedMetrics.average_time_loss);

                // 更新一般车辆指标
                const vehMetrics = networkMetrics.vehicle_metrics || {};
                safeUpdateMetric('vehTravelTime', vehMetrics.average_travel_time);
                safeUpdateMetric('vehWaitingTime', vehMetrics.average_waiting_time);
                safeUpdateMetric('vehWaitingCount', vehMetrics.average_waiting_count, '-', 1);
                safeUpdateMetric('vehTimeLoss', vehMetrics.average_time_loss);

                // 更新贵宾专车指标
                const vipMetrics = networkMetrics.vip_vehicle_metrics || {};
                safeUpdateMetric('vipTravelTime', vipMetrics.average_travel_time);
                safeUpdateMetric('vipWaitingTime', vipMetrics.average_waiting_time);
                safeUpdateMetric('vipWaitingCount', vipMetrics.average_waiting_count, '-', 1);
                safeUpdateMetric('vipTimeLoss', vipMetrics.average_time_loss);

                // 更新场馆区域指标
                const venueMetrics = networkMetrics.venue_area_metrics || {};
                safeUpdateMetric('venuePedTime', venueMetrics.average_pedestrian_travel_time);
                safeUpdateMetric('venuePedDelay', venueMetrics.average_pedestrian_delay);
                safeUpdateMetric('venueVehTime', venueMetrics.average_vehicle_travel_time);
                safeUpdateMetric('venueVehDelay', venueMetrics.average_vehicle_delay);
                // 检查并显示路段分析结果
                if (results.selected_edge_metrics) {
                    displayEdgeAnalysisResults(results.selected_edge_metrics);
                }



                // 创建柱状图（使用安全的图表创建函数）
                safeCreateChart('createPedestrianChart', pedMetrics);
                safeCreateChart('createVehicleChart', vehMetrics);
                safeCreateChart('createVIPVehicleChart', vipMetrics);
                safeCreateChart('createVenueChart', venueMetrics);
            }

            // 安全创建图表
            function safeCreateChart(chartFunctionName, metrics) {
                try {
                    // 确保指标有默认值
                    const safeMetrics = ensureMetricsDefaults(metrics || {});

                    // 检查图表创建函数是否存在
                    if (typeof window[chartFunctionName] === 'function') {
                        window[chartFunctionName](safeMetrics);
                    } else {
                        console.warn(`图表创建函数 ${chartFunctionName} 不存在`);
                    }
                } catch (error) {
                    console.error(`创建图表时出错 (${chartFunctionName}):`, error);
                }
            }

            // 显示路段分析结果
            function displayEdgeAnalysisResults(edgeMetrics) {
                const edgeSection = document.getElementById('edgeAnalysisSection');
                if (edgeSection) {
                    edgeSection.style.display = 'block';

                    // 安全更新路段指标的辅助函数
                    function safeUpdateEdgeMetric(elementId, value, defaultValue = '-', isPercentage = false, decimals = 2) {
                        const element = document.getElementById(elementId);
                        if (element) {
                            if (value !== undefined && value !== null && !isNaN(value)) {
                                if (isPercentage) {
                                    element.textContent = (Number(value) * 100).toFixed(1) + '%';
                                } else {
                                    element.textContent = Number(value).toFixed(decimals);
                                }
                            } else {
                                element.textContent = defaultValue;
                            }
                        }
                    }

                    // 更新路段行人指标
                    const edgePedMetrics = edgeMetrics.pedestrian_metrics || {};
                    safeUpdateEdgeMetric('edgePedTravelTime', edgePedMetrics.avg_traveltime);
                    safeUpdateEdgeMetric('edgePedWaitingTime', edgePedMetrics.avg_waitingTime);
                    safeUpdateEdgeMetric('edgePedSpeed', edgePedMetrics.avg_speed);
                    safeUpdateEdgeMetric('edgePedTimeLoss', edgePedMetrics.avg_timeLoss);
                    safeUpdateEdgeMetric('edgePedCount', edgePedMetrics.total_entered, '-', false, 0);
                    safeUpdateEdgeMetric('edgePedOccupancy', edgePedMetrics.avg_occupancy, '-', true);

                    // 更新路段车辆指标
                    const edgeVehMetrics = edgeMetrics.vehicle_metrics || {};
                    safeUpdateEdgeMetric('edgeVehTravelTime', edgeVehMetrics.avg_traveltime);
                    safeUpdateEdgeMetric('edgeVehWaitingTime', edgeVehMetrics.avg_waitingTime);
                    safeUpdateEdgeMetric('edgeVehSpeed', edgeVehMetrics.avg_speed);
                    safeUpdateEdgeMetric('edgeVehTimeLoss', edgeVehMetrics.avg_timeLoss);
                    safeUpdateEdgeMetric('edgeVehCount', edgeVehMetrics.total_entered, '-', false, 0);
                    safeUpdateEdgeMetric('edgeVehOccupancy', edgeVehMetrics.avg_occupancy, '-', true);
                } else {
                    console.warn('未找到路段分析结果显示区域');
                }
            }



            // 修正数据确保有默认值
            function ensureMetricsDefaults(metrics) {
                const defaults = {
                    average_travel_time: 0,
                    average_waiting_time: 0,
                    average_waiting_count: 0,
                    average_time_loss: 0,
                    average_pedestrian_travel_time: 0,
                    average_pedestrian_delay: 0,
                    average_vehicle_travel_time: 0,
                    average_vehicle_delay: 0
                };

                return { ...defaults, ...metrics };
            }

            // 创建行人指标柱状图
            function createPedestrianChart(metrics) {
                metrics = ensureMetricsDefaults(metrics);
                const ctx = document.getElementById('pedChart').getContext('2d');

                console.log("创建行人图表，容器:", document.getElementById('pedChart'));

                // 确保图表容器可见且有尺寸
                const chartContainer = document.getElementById('pedChart').parentElement;
                if (chartContainer) {
                    // 确保图表容器有明确的高度
                    if (getComputedStyle(chartContainer).height === '0px') {
                        chartContainer.style.height = '200px';
                    }
                }

                // 销毁已存在的图表实例，避免重复创建
                if (window.pedestrianChart instanceof Chart) {
                    window.pedestrianChart.destroy();
                }

                window.pedestrianChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [{
                            label: '行人指标',
                            data: [
                                metrics.average_travel_time,
                                metrics.average_waiting_time,
                                metrics.average_waiting_count,
                                metrics.average_time_loss
                            ],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 206, 86, 0.8)',
                                'rgba(75, 192, 192, 0.8)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#ffffff' // 设置y轴刻度文字颜色为白色
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#ffffff' // 设置x轴刻度文字颜色为白色
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: {
                                    color: '#ffffff' // 设置图例文字颜色
                                }
                            },
                            title: {
                                display: true,
                                text: '行人指标',
                                color: '#ffffff' // 设置标题文字颜色为白色
                            }
                        }
                    }
                });

                console.log("行人图表创建完成", window.pedestrianChart);
            }

            // 创建一般车辆指标柱状图
            function createVehicleChart(metrics) {
                metrics = ensureMetricsDefaults(metrics);
                const ctx = document.getElementById('vehChart').getContext('2d');

                console.log("创建车辆图表，容器:", document.getElementById('vehChart'));
                console.log("指标数据:", metrics);

                // 确保图表容器可见且有尺寸
                const chartContainer = document.getElementById('vehChart').parentElement;
                if (chartContainer) {
                    // 确保图表容器有明确的高度
                    if (getComputedStyle(chartContainer).height === '0px') {
                        chartContainer.style.height = '200px';
                    }
                }

                // 销毁已存在的图表实例，避免重复创建
                if (window.vehicleChart instanceof Chart) {
                    window.vehicleChart.destroy();
                }

                window.vehicleChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [{
                            label: '一般车辆指标',
                            data: [
                                metrics.average_travel_time,
                                metrics.average_waiting_time,
                                metrics.average_waiting_count,
                                metrics.average_time_loss
                            ],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 206, 86, 0.8)',
                                'rgba(75, 192, 192, 0.8)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#ffffff' // 设置y轴刻度文字颜色为白色
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#ffffff' // 设置x轴刻度文字颜色为白色
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: {
                                    color: '#ffffff' // 设置图例文字颜色
                                }
                            },
                            title: {
                                display: true,
                                text: '一般车辆指标',
                                color: '#ffffff' // 设置标题文字颜色为白色
                            }
                        }
                    }
                });

                console.log("车辆图表创建完成", window.vehicleChart);
            }

            // 创建贵宾专车指标柱状图
            function createVIPVehicleChart(metrics) {
                metrics = ensureMetricsDefaults(metrics);
                const ctx = document.getElementById('vipChart').getContext('2d');

                console.log("创建贵宾专车图表，容器:", document.getElementById('vipChart'));

                // 确保图表容器可见且有尺寸
                const chartContainer = document.getElementById('vipChart').parentElement;
                if (chartContainer) {
                    // 确保图表容器有明确的高度
                    if (getComputedStyle(chartContainer).height === '0px') {
                        chartContainer.style.height = '200px';
                    }
                }

                // 销毁已存在的图表实例，避免重复创建
                if (window.vipVehicleChart instanceof Chart) {
                    window.vipVehicleChart.destroy();
                }

                window.vipVehicleChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [{
                            label: '贵宾专车指标',
                            data: [
                                metrics.average_travel_time,
                                metrics.average_waiting_time,
                                metrics.average_waiting_count,
                                metrics.average_time_loss
                            ],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 206, 86, 0.8)',
                                'rgba(75, 192, 192, 0.8)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#ffffff' // 设置y轴刻度文字颜色为白色
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#ffffff' // 设置x轴刻度文字颜色为白色
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: {
                                    color: '#ffffff' // 设置图例文字颜色
                                }
                            },
                            title: {
                                display: true,
                                text: '贵宾专车指标',
                                color: '#ffffff' // 设置标题文字颜色为白色
                            }
                        }
                    }
                });

                console.log("贵宾专车图表创建完成", window.vipVehicleChart);
            }

            // 创建场馆区域指标柱状图
            function createVenueChart(metrics) {
                metrics = ensureMetricsDefaults({
                    average_pedestrian_travel_time: metrics.average_pedestrian_travel_time,
                    average_pedestrian_delay: metrics.average_pedestrian_delay,
                    average_vehicle_travel_time: metrics.average_vehicle_travel_time,
                    average_vehicle_delay: metrics.average_vehicle_delay
                });
                const ctx = document.getElementById('venueChart').getContext('2d');

                console.log("创建场馆区域图表，容器:", document.getElementById('venueChart'));

                // 确保图表容器可见且有尺寸
                const chartContainer = document.getElementById('venueChart').parentElement;
                if (chartContainer) {
                    // 确保图表容器有明确的高度
                    if (getComputedStyle(chartContainer).height === '0px') {
                        chartContainer.style.height = '200px';
                    }
                }

                // 销毁已存在的图表实例，避免重复创建
                if (window.venueChart instanceof Chart) {
                    window.venueChart.destroy();
                }

                window.venueChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['行人平均行程时间', '行人平均延误', '车辆平均行程时间', '车辆平均延误'],
                        datasets: [{
                            label: '场馆区域指标',
                            data: [
                                metrics.average_pedestrian_travel_time,
                                metrics.average_pedestrian_delay,
                                metrics.average_vehicle_travel_time,
                                metrics.average_vehicle_delay
                            ],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 206, 86, 0.8)',
                                'rgba(75, 192, 192, 0.8)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#ffffff' // 设置y轴刻度文字颜色为白色
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#ffffff' // 设置x轴刻度文字颜色为白色
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: {
                                    color: '#ffffff' // 设置图例文字颜色
                                }
                            },
                            title: {
                                display: true,
                                text: '场馆区域指标',
                                color: '#ffffff' // 设置标题文字颜色为白色
                            }
                        }
                    }
                });

                console.log("场馆区域图表创建完成", window.venueChart);
            }
        });
    </script>
</body>
</html>