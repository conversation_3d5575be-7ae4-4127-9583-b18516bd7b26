document.addEventListener('DOMContentLoaded', function() {
    // 元素引用
    const netPreset = document.getElementById('net-preset');
    const netCustom = document.getElementById('net-custom');
    const uploadNetBtn = document.getElementById('uploadNetBtn');

    const trafficPreset = document.getElementById('traffic-preset');
    const trafficCustom = document.getElementById('traffic-custom');
    const uploadRouBtn = document.getElementById('uploadRouBtn');

    const vehicleType = document.getElementById('vehicleType');
    const entranceType = document.getElementById('entranceType');
    const vipPriority = document.getElementById('vipPriority');

    const signalPreset = document.getElementById('signal-preset');
    const signalCustom = document.getElementById('signal-custom');
    const uploadAddBtn = document.getElementById('uploadAddBtn');

    const roadRestriction = document.getElementById('roadRestriction');
    const signalOptimization = document.getElementById('signalOptimization');

    const runBtn = document.querySelector('.run-btn');
    const loadResultBtn = document.getElementById('loadResultBtn');
    const historyBtn = document.getElementById('historyBtn');

    // 新增：路段分析相关元素
    const edgeAnalysis = document.getElementById('edgeAnalysis');
    const edgeAnalysisDetails = document.getElementById('edgeAnalysisDetails');
    const selectEdgesBtn = document.getElementById('selectEdgesBtn');

    // 存储上传的文件路径
    let uploadedNetFile = null;
    let uploadedRouFile = null;
    let uploadedAddFile = null;

    // 存储选中的限行路段和信控优化交叉口
    let selectedRestrictedEdges = [];
    let selectedIntersections = [];

    // 新增：存储选中的分析路段
    let selectedAnalysisEdges = [];

    // 将变量暴露到全局作用域，供index.html中的函数使用
    window.selectedRestrictedEdges = selectedRestrictedEdges;
    window.selectedIntersections = selectedIntersections;

    window.selectedAnalysisEdges = selectedAnalysisEdges;

    // 初始状态设置
    updateEntranceTypeState();
    updateVipPriorityState();

    // 确保内容适应屏幕高度
    // adjustContentToFitScreen();
    // window.addEventListener('resize', adjustContentToFitScreen);

    // 事件监听器
    netPreset.addEventListener('change', updateEntranceTypeState);
    netCustom.addEventListener('change', updateEntranceTypeState);
    vehicleType.addEventListener('change', updateVipPriorityState);

    // 上传按钮事件
    uploadNetBtn.addEventListener('click', function() {
        if (netCustom.checked) {
            simulateFileUpload('net');
        } else {
            showNotification('请先选择"自定义"选项', 'error');
        }
    });

    uploadRouBtn.addEventListener('click', function() {
        if (trafficCustom.checked) {
            simulateFileUpload('rou');
        } else {
            showNotification('请先选择"自定义"选项', 'error');
        }
    });

    uploadAddBtn.addEventListener('click', function() {
        if (signalCustom.checked) {
            simulateFileUpload('add');
        } else {
            showNotification('请先选择"自定义"选项', 'error');
        }
    });

    // 加载已有结果按钮事件
    loadResultBtn.addEventListener('click', loadExistingResult);

    // 限行道路按钮事件
    roadRestriction.addEventListener('change', function() {
        if (this.checked) {
            selectRestrictedEdges();
        } else {
            selectedRestrictedEdges = [];
        }
    });

    // 信控优化按钮事件
    signalOptimization.addEventListener('change', function() {
        if (this.checked) {
            selectIntersections();
        } else {
            selectedIntersections = [];
        }
    });

    // 新增：路段分析事件监听器
    edgeAnalysis.addEventListener('change', function() {
        if (this.checked) {
            edgeAnalysisDetails.style.display = 'block';
        } else {
            edgeAnalysisDetails.style.display = 'none';
            selectedAnalysisEdges = [];
            window.selectedAnalysisEdges = selectedAnalysisEdges;
            // 清除选择状态显示
            updateSelectionStatus('edgeAnalysis', 0);
        }
    });
    // 新增：选择分析路段按钮事件
    selectEdgesBtn.addEventListener('click', function() {
        if (edgeAnalysis.checked) {
            selectAnalysisEdges();
        } else {
            showNotification('请先启用"用户自选路段指标分析"', 'error');
        }
    });

    // 运行按钮事件
    runBtn.addEventListener('click', startSimulation);

    // 功能函数
    function updateEntranceTypeState() {
        entranceType.disabled = !netPreset.checked;
        if (!netPreset.checked) {
            entranceType.parentElement.parentElement.classList.add('disabled');
        } else {
            entranceType.parentElement.parentElement.classList.remove('disabled');
        }
    }

    function updateVipPriorityState() {
        const hasVIP = vehicleType.value === 'vip';
        vipPriority.disabled = !hasVIP;
        if (!hasVIP) {
            vipPriority.checked = false;
            vipPriority.parentElement.parentElement.classList.add('disabled');
        } else {
            vipPriority.parentElement.parentElement.classList.remove('disabled');
        }
    }

    function adjustContentToFitScreen() {
        const container = document.querySelector('.container');
        const windowHeight = window.innerHeight;
        const contentHeight = container.scrollHeight;

        // 如果内容高度超过窗口高度，调整容器的缩放比例
        if (contentHeight > windowHeight) {
            const scale = Math.min(0.95, windowHeight / contentHeight);
            container.style.transform = `scale(${scale})`;
            container.style.transformOrigin = 'top center';
            container.style.marginBottom = `${(windowHeight - contentHeight * scale) / 2}px`;
        } else {
            container.style.transform = '';
            container.style.marginBottom = '';
        }
    }

    function simulateFileUpload(fileType) {
        const input = document.createElement('input');
        input.type = 'file';

        let fileExtension = '';
        let acceptTypes = '';

        switch(fileType) {
            case 'net':
                fileExtension = '.net.xml';
                acceptTypes = '.net.xml';
                break;
            case 'rou':
                fileExtension = '.rou.xml';
                acceptTypes = '.rou.xml';
                break;
            case 'add':
                fileExtension = '.add.xml';
                acceptTypes = '.add.xml';
                break;
        }

        input.accept = acceptTypes;

        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                // 验证文件扩展名
                if (!file.name.toLowerCase().endsWith(fileExtension)) {
                    showNotification(`文件类型错误，请选择${fileExtension}文件`, 'error');
                    return;
                }

                // 检查文件大小（限制为10MB）
                if (file.size > 10 * 1024 * 1024) {
                    showNotification('文件过大，请选择小于10MB的文件', 'error');
                    return;
                }

                // 验证文件内容（这里只是模拟，实际中可以读取文件内容进行更深入的验证）
                validateFileContent(file, fileType)
                    .then(isValid => {
                        if (isValid) {
                            // 存储文件路径（此处使用文件名模拟路径）
                            switch(fileType) {
                                case 'net':
                                    uploadedNetFile = file.name;
                                    break;
                                case 'rou':
                                    uploadedRouFile = file.name;
                                    break;
                                case 'add':
                                    uploadedAddFile = file.name;
                                    break;
                            }

                            showNotification(`已选择文件: ${file.name}`, 'success');
                        } else {
                            showNotification(`文件内容无效，请选择有效的${fileExtension}文件`, 'error');
                        }
                    })
                    .catch(error => {
                        showNotification('文件验证过程中发生错误', 'error');
                        console.error(error);
                    });
            }
        };

        input.click();
    }

    // 加载已有结果文件
    function loadExistingResult() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                // 验证文件扩展名
                if (!file.name.toLowerCase().endsWith('.json')) {
                    showNotification('文件类型错误，请选择JSON格式文件', 'error');
                    return;
                }

                // 检查文件大小（限制为10MB）
                if (file.size > 10 * 1024 * 1024) {
                    showNotification('文件过大，请选择小于10MB的文件', 'error');
                    return;
                }

                // 读取JSON文件内容
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        // 尝试解析JSON
                        const jsonData = JSON.parse(e.target.result);

                        // 验证JSON结构
                        if (validateResultJson(jsonData)) {
                            showNotification('正在加载结果文件...', 'info');

                            // 转换为统一格式（新格式）
                            const normalizedData = normalizeResultJson(jsonData);

                            // 如果没有config_summary，从config生成简化摘要
                            if (!normalizedData.config_summary && normalizedData.config) {
                                normalizedData.config_summary = generateConfigSummaryFromConfig(normalizedData.config);
                            }

                            // 如果没有start_time，使用当前时间
                            if (!normalizedData.start_time) {
                                normalizedData.start_time = new Date().toISOString();
                            }

                            // 直接在当前页面显示结果，而不是跳转
                            updateDisplay(normalizedData);
                            showNotification('结果已加载', 'success');
                        } else {
                            showNotification('JSON格式无效，请提供有效的结果文件', 'error');
                        }
                    } catch (error) {
                        showNotification('无法解析JSON文件', 'error');
                        console.error('JSON解析错误:', error);
                    }
                };

                reader.onerror = function() {
                    showNotification('读取文件时发生错误', 'error');
                };

                reader.readAsText(file);
            }
        };

        input.click();
    }

    // 验证结果JSON的结构 - 支持新旧格式兼容
    function validateResultJson(json) {
        console.log('验证JSON结构:', json);

        // 检查必要的字段是否存在
        const requiredFields = ['simulation_id', 'simulation_results'];
        const missingFields = [];

        for (const field of requiredFields) {
            if (!json[field]) {
                missingFields.push(field);
            }
        }

        if (missingFields.length > 0) {
            console.error('缺少必要字段:', missingFields);
            console.log('JSON包含的字段:', Object.keys(json));
            return false;
        }

        // 检查仿真结果字段
        const results = json.simulation_results;
        if (!results) {
            console.error('simulation_results字段不存在');
            return false;
        }

        // 检查是否为新格式（包含network_metrics）
        if (results.network_metrics) {
            console.log('检测到新格式JSON');
            const networkMetrics = results.network_metrics;

            // 验证新格式的必要字段
            if (!networkMetrics.pedestrian_metrics && !networkMetrics.vehicle_metrics) {
                console.error('新格式中network_metrics缺少行人和车辆指标');
                return false;
            }

            // 检查是否包含路段分析结果（可选）
            if (results.selected_edge_metrics) {
                console.log('包含路段分析结果');
            }
        } else {
            // 检查是否为旧格式（直接包含指标）
            console.log('检测到旧格式JSON，尝试验证...');

            // 旧格式至少需要包含行人或车辆指标之一
            if (!results.pedestrian_metrics && !results.vehicle_metrics) {
                console.error('旧格式中缺少行人和车辆指标');
                console.log('simulation_results包含的字段:', Object.keys(results));
                return false;
            }

            console.log('旧格式JSON验证通过');
        }

        console.log('JSON验证通过');
        return true;
    }

    // 将结果JSON标准化为新格式
    function normalizeResultJson(json) {
        console.log('标准化JSON格式:', json);

        // 如果已经是新格式，直接返回
        if (json.simulation_results && json.simulation_results.network_metrics) {
            console.log('已是新格式，无需转换');
            return json;
        }

        // 转换旧格式为新格式
        console.log('转换旧格式为新格式');
        const normalizedJson = {
            simulation_id: json.simulation_id,
            config: json.config || {},
            config_summary: json.config_summary,
            start_time: json.start_time,
            simulation_results: {
                network_metrics: {
                    pedestrian_metrics: json.simulation_results.pedestrian_metrics || {},
                    vehicle_metrics: json.simulation_results.vehicle_metrics || {},
                    vip_vehicle_metrics: json.simulation_results.vip_vehicle_metrics || {},
                    venue_area_metrics: json.simulation_results.venue_area_metrics || {}
                }
            }
        };

        // 如果存在路段分析结果，也复制过来
        if (json.simulation_results.selected_edge_metrics) {
            normalizedJson.simulation_results.selected_edge_metrics = json.simulation_results.selected_edge_metrics;
        }

        console.log('格式转换完成:', normalizedJson);
        return normalizedJson;
    }

    // 从完整config生成简化的配置摘要
    function generateConfigSummaryFromConfig(config) {
        const summary = {
            network: '未知',
            signal: '未知',
            traffic: '未知'
        };

        try {
            // 提取路网信息
            if (config.network_config) {
                if (config.network_config.type === 'predefined') {
                    summary.network = '预设路网';
                } else {
                    summary.network = '自定义路网';
                }

                // 添加出入口信息
                if (config.network_config.entrance_plan) {
                    summary.network += ` (${config.network_config.entrance_plan})`;
                }
            }

            // 提取信号灯信息
            if (config.signal_config) {
                if (config.signal_config.optimization && config.signal_config.optimization.enabled) {
                    summary.signal = '启用优化';
                } else {
                    summary.signal = '未优化';
                }
            }

            // 提取交通配置信息
            const measures = [];
            if (config.traffic_config) {
                if (config.traffic_config.scenario) {
                    measures.push(config.traffic_config.scenario);
                }
                if (config.traffic_config.vehicle_type) {
                    measures.push(config.traffic_config.vehicle_type);
                }
                if (config.traffic_config.vip_priority && config.traffic_config.vip_priority.enabled) {
                    measures.push('贵宾优先');
                }
            }

            // 检查道路限行
            if (config.network_config && config.network_config.road_restriction && config.network_config.road_restriction.enabled) {
                measures.push('道路限行');
            }

            summary.traffic = measures.length > 0 ? measures.join(', ') : '无特殊配置';

        } catch (error) {
            console.warn('生成配置摘要时出错:', error);
        }

        return summary;
    }

    function validateFileContent(file, fileType) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = function(e) {
                const content = e.target.result;

                // 简单检查XML格式和关键标签
                let isValid = content.trim().startsWith('<?xml');

                if (isValid) {
                    switch(fileType) {
                        case 'net':
                            isValid = content.includes('<net') || content.includes('<network');
                            break;
                        case 'rou':
                            isValid = content.includes('<routes') || content.includes('<flows');
                            break;
                        case 'add':
                            isValid = content.includes('<additional') || content.includes('<additionals');
                            break;
                    }
                }

                resolve(isValid);
            };

            reader.onerror = function() {
                reject(new Error('文件读取错误'));
            };

            reader.readAsText(file);
        });
    }

    function getCurrentNetFilePath() {
        // 返回当前使用的路网文件路径
        if (netCustom.checked && uploadedNetFile) {
            return uploadedNetFile;
        } else {
            // 返回预设路网路径（根据出入口选择不同的预设文件）
            const entranceValue = entranceType.value;
            return `network_${entranceValue}.net.xml`; // 例如：network_east.net.xml
        }
    }

    // 选择分析路段
    async function selectAnalysisEdges() {
        // 首先获取当前路网文件路径
        const netFilePath = getCurrentNetFilePath();
        if (!netFilePath) {
            showError('请先选择或上传路网文件');
            edgeAnalysis.checked = false;
            return;
        }

        // 显示加载中
        showLoading('正在启动路段选择器...');

        try {
            // 模拟API调用
            console.log('调用路段选择器API，路网文件:', netFilePath);

            // 模拟API请求
            // 实际中应该是:
            /*
            const response = await fetch('http://localhost:8888/api/network/select', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    net_file_path: netFilePath,
                    selector_type: 'edge'
                }),
                timeout: 300000  // 5分钟超时，等待用户选择
            });

            const result = await response.json();
            */

            // 模拟延时，表示用户在选择器中进行选择
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 模拟用户选择了多个路段
            const result = {
                success: true,
                count: 4,
                selected_ids: ['980398774#10', '980398774#6', '980398780#1', '-980398780#1']
            };

            // 处理选择结果
            if (result.success) {
                // 更新选中的路段列表
                selectedAnalysisEdges = result.selected_ids;

                // 更新全局变量
                window.selectedAnalysisEdges = selectedAnalysisEdges;

                // 更新选择状态显示
                updateSelectionStatus('edgeAnalysis', selectedAnalysisEdges.length);

                // 显示成功通知
                showNotification(`已选择 ${selectedAnalysisEdges.length} 个路段用于分析`, 'success');
            } else {
                showError(result.error || '选择路段失败');
                edgeAnalysis.checked = false;
            }
        } catch (error) {
            console.error('路段选择器调用失败:', error);
            showError('启动路段选择器失败，请稍后重试');
            edgeAnalysis.checked = false;
        } finally {
            hideLoading();
        }
    }

    // 选择限行道路
    async function selectRestrictedEdges() {
        // 首先获取当前路网文件路径
        const netFilePath = getCurrentNetFilePath();
        if (!netFilePath) {
            showError('请先选择或上传路网文件');
            roadRestriction.checked = false;
            return;
        }

        // 模拟打开路段选择弹窗
        showNotification('请选择需要限行的道路', 'info');

        // 模拟异步选择过程
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 模拟用户选择了2个路段
        selectedRestrictedEdges = ['edge_1', 'edge_2'];

        // 更新全局变量
        window.selectedRestrictedEdges = selectedRestrictedEdges;

        // 更新选择状态显示
        updateSelectionStatus('roadRestriction', selectedRestrictedEdges.length);

        // 完成选择
        showNotification(`已选择 ${selectedRestrictedEdges.length} 个限行路段`, 'success');
    }

    // 选择信控优化交叉口
    async function selectIntersections() {
        // 首先获取当前路网文件路径
        const netFilePath = getCurrentNetFilePath();
        if (!netFilePath) {
            showError('请先选择或上传路网文件');
            signalOptimization.checked = false;
            return;
        }

        // 模拟打开交叉口选择弹窗
        showNotification('请选择需要优化信号灯的交叉口', 'info');

        // 模拟异步选择过程
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 模拟用户选择了2个交叉口
        selectedIntersections = ['junction_1', 'junction_2'];

        // 更新全局变量
        window.selectedIntersections = selectedIntersections;

        // 更新选择状态显示
        updateSelectionStatus('signalOptimization', selectedIntersections.length);

        // 完成选择
        showNotification(`已选择 ${selectedIntersections.length} 个交叉口进行信控优化`, 'success');
    }

    // 更新选择状态显示
    function updateSelectionStatus(elementId, count) {
        // 获取对应的状态元素
        const statusElement = document.getElementById(`${elementId}Status`);
        if (statusElement) {
            if (count > 0) {
                statusElement.textContent = `(已选择 ${count} 项)`;
                statusElement.style.display = 'inline';
                statusElement.style.color = '#4caf50';
                statusElement.style.marginLeft = '5px';
                statusElement.style.fontSize = '0.9em';
            } else {
                statusElement.style.display = 'none';
            }
        }
    }

    // 开始仿真
    async function startSimulation() {
        // 显示加载中
        showLoading('正在构建仿真环境...');

        try {
            // 构建配置JSON
            const config = window.buildConfigJSON ? window.buildConfigJSON() : getDefaultConfig();
            console.log('构建的配置:', config);

            // 模拟API调用
            await new Promise(resolve => setTimeout(resolve, 2000));
            showLoading('正在进行仿真计算...');
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 模拟返回结果
            const result = {
                simulation_id: `${new Date().toISOString().replace(/[-:\.]/g, '').slice(2, 14)}`,
                config: config,
                config_summary: generateConfigSummaryFromConfig(config),
                simulation_results: {
                    network_metrics: {
                        pedestrian_metrics: { average_travel_time: 123.45, average_waiting_time: 12.34, average_waiting_count: 2.1, average_time_loss: 23.45 },
                        vehicle_metrics: { average_travel_time: 234.56, average_waiting_time: 45.67, average_waiting_count: 3.2, average_time_loss: 67.89 },
                        vip_vehicle_metrics: { average_travel_time: 145.23, average_waiting_time: 8.91, average_waiting_count: 1.2, average_time_loss: 12.34 },
                        venue_area_metrics: { average_pedestrian_travel_time: 156.78, average_pedestrian_delay: 34.56, average_vehicle_travel_time: 267.89, average_vehicle_delay: 78.90 }
                    }
                }
            };

            // 如果启用了路段分析，添加相应结果
            if (config.analysis_config && config.analysis_config.edge_analysis && config.analysis_config.edge_analysis.enabled && config.analysis_config.edge_analysis.selected_edges.length > 0) {
                result.simulation_results.selected_edge_metrics = {
                    pedestrian_metrics: {
                        total_departed: 150.0,
                        total_arrived: 148.0,
                        total_entered: 160.0,
                        avg_traveltime: 45.5,
                        avg_waitingTime: 12.3,
                        avg_speed: 8.2,
                        avg_timeLoss: 15.7,
                        avg_occupancy: 0.23
                    },
                    vehicle_metrics: {
                        total_departed: 89.0,
                        total_arrived: 87.0,
                        total_entered: 95.0,
                        avg_traveltime: 78.9,
                        avg_waitingTime: 28.4,
                        avg_speed: 6.8,
                        avg_timeLoss: 45.6,
                        avg_occupancy: 1.45
                    }
                };
            }

            // 隐藏加载
            hideLoading();

            // 显示结果
            updateDisplay(result);
            showNotification('仿真计算完成', 'success');
        } catch (error) {
            hideLoading();
            showError('仿真计算失败: ' + error.message);
            console.error('仿真错误:', error);
        }
    }

    // 显示加载中
    function showLoading(message) {
        // 检查是否已存在加载层
        let loadingOverlay = document.getElementById('loadingOverlay');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'loadingOverlay';
            loadingOverlay.innerHTML = `
                <div class="loading-spinner"></div>
                <div class="loading-message" id="loadingMessage"></div>
            `;

            document.body.appendChild(loadingOverlay);

            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                #loadingOverlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.7);
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                }
                .loading-spinner {
                    border: 5px solid #f3f3f3;
                    border-top: 5px solid #3498db;
                    border-radius: 50%;
                    width: 50px;
                    height: 50px;
                    animation: spin 2s linear infinite;
                }
                .loading-message {
                    color: white;
                    margin-top: 20px;
                    font-size: 18px;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        // 更新加载消息
        document.getElementById('loadingMessage').textContent = message;
        loadingOverlay.style.display = 'flex';
    }

    // 隐藏加载中
    function hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    // 显示错误信息
    function showError(message) {
        alert(`错误: ${message}`);
    }

    // 显示成功提示
    function showSuccess(message) {
        alert(`成功: ${message}`);
    }

    // 在当前页面显示结果数据
    function updateDisplay(data) {
        console.log('更新显示结果:', data);

        // 添加数据加载标记
        window.resultDataLoaded = true;

        // 更新仿真信息
        document.getElementById('simId').textContent = data.simulation_id;

        // 格式化并显示时间
        const startTimeRaw = new Date(data.start_time);
        const formattedTime = startTimeRaw.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        }).replace(/\//g, '-');
        document.getElementById('startTime').textContent = formattedTime;

        // 更新配置摘要
        if (data.config_summary) {
            const networkElement = document.querySelector('#networkConfig span');
            const signalElement = document.querySelector('#signalConfig span');
            const trafficElement = document.querySelector('#trafficConfig span');

            if (networkElement) networkElement.textContent = data.config_summary.network || '未知';
            if (signalElement) signalElement.textContent = data.config_summary.signal || '未知';
            if (trafficElement) trafficElement.textContent = data.config_summary.traffic || '未知';
        }

        // 获取指标数据
        const results = data.simulation_results;

        // 兼容性处理：检查新旧格式
        let networkMetrics;
        if (results.network_metrics) {
            // 新格式
            networkMetrics = results.network_metrics;
        } else {
            // 旧格式，构造兼容结构
            networkMetrics = {
                pedestrian_metrics: results.pedestrian_metrics || {},
                vehicle_metrics: results.vehicle_metrics || {},
                vip_vehicle_metrics: results.vip_vehicle_metrics || {},
                venue_area_metrics: results.venue_area_metrics || {}
            };
        }

        // 安全更新指标的辅助函数
        function safeUpdateMetric(elementId, value, defaultValue = '-', decimals = 2) {
            const element = document.getElementById(elementId);
            if (element) {
                if (value !== undefined && value !== null && !isNaN(value)) {
                    element.textContent = Number(value).toFixed(decimals);
                } else {
                    element.textContent = defaultValue;
                }
            }
        }

        // 更新行人指标
        const pedMetrics = networkMetrics.pedestrian_metrics || {};
        safeUpdateMetric('pedTravelTime', pedMetrics.average_travel_time);
        safeUpdateMetric('pedWaitingTime', pedMetrics.average_waiting_time);
        safeUpdateMetric('pedWaitingCount', pedMetrics.average_waiting_count, '-', 1);
        safeUpdateMetric('pedTimeLoss', pedMetrics.average_time_loss);

        // 更新一般车辆指标
        const vehMetrics = networkMetrics.vehicle_metrics || {};
        safeUpdateMetric('vehTravelTime', vehMetrics.average_travel_time);
        safeUpdateMetric('vehWaitingTime', vehMetrics.average_waiting_time);
        safeUpdateMetric('vehWaitingCount', vehMetrics.average_waiting_count, '-', 1);
        safeUpdateMetric('vehTimeLoss', vehMetrics.average_time_loss);

        // 更新贵宾专车指标
        const vipMetrics = networkMetrics.vip_vehicle_metrics || {};
        safeUpdateMetric('vipTravelTime', vipMetrics.average_travel_time);
        safeUpdateMetric('vipWaitingTime', vipMetrics.average_waiting_time);
        safeUpdateMetric('vipWaitingCount', vipMetrics.average_waiting_count, '-', 1);
        safeUpdateMetric('vipTimeLoss', vipMetrics.average_time_loss);

        // 更新场馆区域指标
        const venueMetrics = networkMetrics.venue_area_metrics || {};
        safeUpdateMetric('venuePedTime', venueMetrics.average_pedestrian_travel_time);
        safeUpdateMetric('venuePedDelay', venueMetrics.average_pedestrian_delay);
        safeUpdateMetric('venueVehTime', venueMetrics.average_vehicle_travel_time);
        safeUpdateMetric('venueVehDelay', venueMetrics.average_vehicle_delay);

        // 检查并显示路段分析结果
        if (results.selected_edge_metrics) {
            displayEdgeAnalysisResults(results.selected_edge_metrics);
        }

        // 创建柱状图（使用安全的图表创建函数）
        safeCreateChart('createPedestrianChart', pedMetrics);
        safeCreateChart('createVehicleChart', vehMetrics);
        safeCreateChart('createVIPVehicleChart', vipMetrics);
        safeCreateChart('createVenueChart', venueMetrics);
    }

    // 确保指标有默认值
    function ensureMetricsDefaults(metrics) {
        const defaults = {
            average_travel_time: 0,
            average_waiting_time: 0,
            average_waiting_count: 0,
            average_time_loss: 0,
            average_pedestrian_travel_time: 0,
            average_pedestrian_delay: 0,
            average_vehicle_travel_time: 0,
            average_vehicle_delay: 0
        };

        return { ...defaults, ...metrics };
    }

    // 安全创建图表
    function safeCreateChart(chartFunctionName, metrics) {
        try {
            // 确保指标有默认值
            const safeMetrics = ensureMetricsDefaults(metrics || {});

            // 检查图表创建函数是否存在
            if (typeof window[chartFunctionName] === 'function') {
                window[chartFunctionName](safeMetrics);
            } else {
                console.warn(`图表创建函数 ${chartFunctionName} 不存在`);
            }
        } catch (error) {
            console.error(`创建图表时出错 (${chartFunctionName}):`, error);
        }
    }

    // 显示路段分析结果
    function displayEdgeAnalysisResults(edgeMetrics) {
        const edgeSection = document.getElementById('edgeAnalysisSection');
        if (edgeSection) {
            edgeSection.style.display = 'block';

            // 安全更新路段指标的辅助函数
            function safeUpdateEdgeMetric(elementId, value, defaultValue = '-', isPercentage = false, decimals = 2) {
                const element = document.getElementById(elementId);
                if (element) {
                    if (value !== undefined && value !== null && !isNaN(value)) {
                        if (isPercentage) {
                            element.textContent = (Number(value) * 100).toFixed(1) + '%';
                        } else {
                            element.textContent = Number(value).toFixed(decimals);
                        }
                    } else {
                        element.textContent = defaultValue;
                    }
                }
            }

            // 更新路段行人指标
            const edgePedMetrics = edgeMetrics.pedestrian_metrics || {};
            safeUpdateEdgeMetric('edgePedTravelTime', edgePedMetrics.avg_traveltime);
            safeUpdateEdgeMetric('edgePedWaitingTime', edgePedMetrics.avg_waitingTime);
            safeUpdateEdgeMetric('edgePedSpeed', edgePedMetrics.avg_speed);
            safeUpdateEdgeMetric('edgePedTimeLoss', edgePedMetrics.avg_timeLoss);
            safeUpdateEdgeMetric('edgePedCount', edgePedMetrics.total_entered, '-', false, 0);
            safeUpdateEdgeMetric('edgePedOccupancy', edgePedMetrics.avg_occupancy, '-', true);

            // 更新路段车辆指标
            const edgeVehMetrics = edgeMetrics.vehicle_metrics || {};
            safeUpdateEdgeMetric('edgeVehTravelTime', edgeVehMetrics.avg_traveltime);
            safeUpdateEdgeMetric('edgeVehWaitingTime', edgeVehMetrics.avg_waitingTime);
            safeUpdateEdgeMetric('edgeVehSpeed', edgeVehMetrics.avg_speed);
            safeUpdateEdgeMetric('edgeVehTimeLoss', edgeVehMetrics.avg_timeLoss);
            safeUpdateEdgeMetric('edgeVehCount', edgeVehMetrics.total_entered, '-', false, 0);
            safeUpdateEdgeMetric('edgeVehOccupancy', edgeVehMetrics.avg_occupancy, '-', true);
        } else {
            console.warn('未找到路段分析结果显示区域');
        }
    }

    // 通知提示
    function showNotification(message, type = 'info') {
        // 检查是否已存在通知容器
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);

            // 添加通知容器样式
            const style = document.createElement('style');
            style.textContent = `
                .notification-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1000;
                }
                .notification {
                    padding: 12px 20px;
                    margin-bottom: 10px;
                    border-radius: 4px;
                    color: white;
                    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
                    animation: slide-in 0.3s ease-out forwards;
                    max-width: 300px;
                }
                .notification.info {
                    background-color: var(--primary-color);
                }
                .notification.success {
                    background-color: var(--secondary-color);
                }
                .notification.error {
                    background-color: #ea4335;
                }
                @keyframes slide-in {
                    0% { transform: translateX(100%); opacity: 0; }
                    100% { transform: translateX(0); opacity: 1; }
                }
                @keyframes fade-out {
                    0% { transform: translateX(0); opacity: 1; }
                    100% { transform: translateX(100%); opacity: 0; }
                }
                .notification.fade-out {
                    animation: fade-out 0.3s ease-in forwards;
                }
            `;
            document.head.appendChild(style);
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        container.appendChild(notification);

        // 3秒后移除通知
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                container.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // 默认配置（备用）
    function getDefaultConfig() {
        return {
            "network_config": {
                "type": "predefined",
                "file_path": null,
                "entrance_plan": "仅开放东侧出入口",
                "road_restriction": {
                    "enabled": false,
                    "restricted_edges": []
                }
            },
            "signal_config": {
                "type": "predefined",
                "file_path": null,
                "optimization": {
                    "enabled": false,
                    "selected_intersections": []
                }
            },
            "traffic_config": {
                "type": "predefined",
                "file_path": null,
                "scenario": "进场",
                "vehicle_type": "仅一般车辆",
                "vip_priority": {
                    "enabled": false
                }
            },
            "analysis_config": {
                "edge_analysis": {
                    "enabled": false,
                    "selected_edges": []
                }
            }
        };
    }

    // ==================== 历史方案管理功能 ====================

    // 历史方案相关的DOM元素
    const historyModal = document.getElementById('historyModal');
    const saveSchemeModal = document.getElementById('saveSchemeModal');
    const closeHistoryModal = document.getElementById('closeHistoryModal');
    const closeSaveModal = document.getElementById('closeSaveModal');
    const saveCurrentBtn = document.getElementById('saveCurrentBtn');
    const refreshHistoryBtn = document.getElementById('refreshHistoryBtn');
    const historyList = document.getElementById('historyList');
    const confirmSaveBtn = document.getElementById('confirmSaveBtn');
    const cancelSaveBtn = document.getElementById('cancelSaveBtn');
    const schemeName = document.getElementById('schemeName');
    const schemeDescription = document.getElementById('schemeDescription');
    const configPreview = document.getElementById('configPreview');

    // 历史方案管理类
    class HistoryManager {
        constructor() {
            this.apiBase = 'http://localhost:8888';
            this.schemes = [];
            this.initializeEventListeners();
        }

        initializeEventListeners() {
            // 历史方案按钮点击
            historyBtn.addEventListener('click', () => {
                this.showHistoryModal();
            });

            // 模态框关闭
            closeHistoryModal.addEventListener('click', () => {
                this.hideHistoryModal();
            });

            closeSaveModal.addEventListener('click', () => {
                this.hideSaveModal();
            });

            // 点击模态框外部关闭
            window.addEventListener('click', (event) => {
                if (event.target === historyModal) {
                    this.hideHistoryModal();
                }
                if (event.target === saveSchemeModal) {
                    this.hideSaveModal();
                }
            });

            // 保存当前配置按钮
            saveCurrentBtn.addEventListener('click', () => {
                this.showSaveModal();
            });

            // 刷新历史列表按钮
            refreshHistoryBtn.addEventListener('click', () => {
                this.loadHistoryList();
            });

            // 确认保存按钮
            confirmSaveBtn.addEventListener('click', () => {
                this.saveCurrentScheme();
            });

            // 取消保存按钮
            cancelSaveBtn.addEventListener('click', () => {
                this.hideSaveModal();
            });
        }

        showHistoryModal() {
            historyModal.style.display = 'block';
            this.loadHistoryList();
        }

        hideHistoryModal() {
            historyModal.style.display = 'none';
        }

        showSaveModal() {
            // 生成配置预览
            const config = window.buildConfigJSON ? window.buildConfigJSON() : getDefaultConfig();
            this.updateConfigPreview(config);

            // 清空表单
            schemeName.value = '';
            schemeDescription.value = '';

            saveSchemeModal.style.display = 'block';
        }

        hideSaveModal() {
            saveSchemeModal.style.display = 'none';
        }

        updateConfigPreview(config) {
            try {
                // 生成配置摘要
                const summary = this.generateConfigSummary(config);
                configPreview.innerHTML = `
                    <div><strong>路网配置:</strong> ${summary.network}</div>
                    <div><strong>信号配置:</strong> ${summary.signal}</div>
                    <div><strong>交通配置:</strong> ${summary.traffic}</div>
                `;
            } catch (error) {
                configPreview.innerHTML = '<div style="color: #dc3545;">配置预览生成失败</div>';
                console.error('配置预览生成失败:', error);
            }
        }

        generateConfigSummary(config) {
            // 网络配置摘要
            const networkConfig = config.network_config || {};
            const networkType = networkConfig.type === 'predefined' ? '预设路网' : '自定义路网';
            const entrancePlan = networkConfig.entrance_plan || '未知';
            const roadRestriction = networkConfig.road_restriction || {};
            let restrictionText = '';
            if (roadRestriction.enabled) {
                const restrictedCount = (roadRestriction.restricted_edges || []).length;
                restrictionText = ` + 道路限行(${restrictedCount}个路段)`;
            }
            const networkSummary = `${networkType} - ${entrancePlan}${restrictionText}`;

            // 信号配置摘要
            const signalConfig = config.signal_config || {};
            const signalType = signalConfig.type === 'predefined' ? '预设配时' : '自定义配时';
            const optimization = signalConfig.optimization || {};
            let optimizationText = '';
            if (optimization.enabled) {
                const intersectionCount = (optimization.selected_intersections || []).length;
                optimizationText = ` + 自定义优化(${intersectionCount}个交叉口)`;
            }
            const signalSummary = `${signalType}${optimizationText}`;

            // 交通配置摘要
            const trafficConfig = config.traffic_config || {};
            const trafficType = trafficConfig.type === 'predefined' ? '预设需求' : '自定义需求';
            const scenario = trafficConfig.scenario || '未知';
            const vehicleType = trafficConfig.vehicle_type || '未知';
            const vipPriority = trafficConfig.vip_priority || {};
            const vipText = vipPriority.enabled ? ' + 贵宾专车' : '';
            const trafficSummary = `${trafficType} - ${scenario}场景 + ${vehicleType}${vipText}`;

            return {
                network: networkSummary,
                signal: signalSummary,
                traffic: trafficSummary
            };
        }

        async loadHistoryList() {
            try {
                historyList.innerHTML = '<div class="loading-message">正在加载历史方案...</div>';

                const response = await fetch(`${this.apiBase}/api/history/list`);
                const result = await response.json();

                if (result.success) {
                    this.schemes = result.schemes;
                    this.renderHistoryList();
                } else {
                    historyList.innerHTML = `<div class="empty-message">加载失败: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('加载历史方案失败:', error);
                historyList.innerHTML = '<div class="empty-message">加载历史方案时发生错误</div>';
            }
        }

        renderHistoryList() {
            if (this.schemes.length === 0) {
                historyList.innerHTML = '<div class="empty-message">暂无保存的历史方案</div>';
                return;
            }

            const html = this.schemes.map(scheme => {
                const createdTime = new Date(scheme.created_time).toLocaleString('zh-CN');
                const description = scheme.description || '无描述';

                return `
                    <div class="history-item" data-scheme-id="${scheme.id}">
                        <div class="history-item-header">
                            <div class="history-item-title">${scheme.name}</div>
                            <div class="history-item-time">${createdTime}</div>
                        </div>
                        <div class="history-item-description">${description}</div>
                        <div class="history-item-config">
                            <div>路网: ${scheme.config_summary.network}</div>
                            <div>信号: ${scheme.config_summary.signal}</div>
                            <div>交通: ${scheme.config_summary.traffic}</div>
                        </div>
                        <div class="history-item-actions">
                            <button class="load-scheme-btn" onclick="historyManager.loadScheme('${scheme.id}')">
                                <i class="bi bi-download"></i> 加载方案
                            </button>
                            <button class="delete-scheme-btn" onclick="historyManager.deleteScheme('${scheme.id}')">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            historyList.innerHTML = html;
        }

        async saveCurrentScheme() {
            try {
                const config = window.buildConfigJSON ? window.buildConfigJSON() : getDefaultConfig();
                const name = schemeName.value.trim();
                const description = schemeDescription.value.trim();

                confirmSaveBtn.textContent = '保存中...';
                confirmSaveBtn.disabled = true;

                const response = await fetch(`${this.apiBase}/api/history/save`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        config: config,
                        name: name || null,
                        description: description || null
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess(`方案保存成功: ${result.message}`);
                    this.hideSaveModal();
                    this.loadHistoryList(); // 刷新历史列表
                } else {
                    showError(`保存失败: ${result.error}`);
                }
            } catch (error) {
                console.error('保存方案失败:', error);
                showError('保存方案时发生错误，请检查网络连接');
            } finally {
                confirmSaveBtn.textContent = '确认保存';
                confirmSaveBtn.disabled = false;
            }
        }

        async loadScheme(schemeId) {
            try {
                showLoading('正在加载方案配置...');

                const response = await fetch(`${this.apiBase}/api/history/get/${schemeId}`);
                const result = await response.json();

                if (result.success) {
                    const scheme = result.scheme;
                    this.applySchemeToUI(scheme.config);
                    showSuccess(`方案 "${scheme.name}" 加载成功`);
                    this.hideHistoryModal();
                } else {
                    showError(`加载方案失败: ${result.error}`);
                }
            } catch (error) {
                console.error('加载方案失败:', error);
                showError('加载方案时发生错误，请检查网络连接');
            } finally {
                hideLoading();
            }
        }

        applySchemeToUI(config) {
            try {
                // 应用网络配置
                const networkConfig = config.network_config || {};
                if (networkConfig.type === 'predefined') {
                    document.getElementById('net-preset').checked = true;
                } else {
                    document.getElementById('net-custom').checked = true;
                }

                // 应用出入口方案
                const entrancePlan = networkConfig.entrance_plan;
                const entranceSelect = document.getElementById('entranceType');
                if (entrancePlan === '仅开放东侧出入口') {
                    entranceSelect.value = 'east';
                } else if (entrancePlan === '仅开放南侧出入口') {
                    entranceSelect.value = 'south';
                } else if (entrancePlan === '全部开放') {
                    entranceSelect.value = 'all';
                }

                // 应用道路限行
                const roadRestriction = networkConfig.road_restriction || {};
                document.getElementById('roadRestriction').checked = roadRestriction.enabled || false;
                selectedRestrictedEdges = roadRestriction.restricted_edges || [];
                window.selectedRestrictedEdges = selectedRestrictedEdges;

                // 应用信号配置
                const signalConfig = config.signal_config || {};
                if (signalConfig.type === 'predefined') {
                    document.getElementById('signal-preset').checked = true;
                } else {
                    document.getElementById('signal-custom').checked = true;
                }

                // 应用信控优化
                const optimization = signalConfig.optimization || {};
                document.getElementById('signalOptimization').checked = optimization.enabled || false;
                selectedIntersections = optimization.selected_intersections || [];
                window.selectedIntersections = selectedIntersections;

                // 应用交通配置
                const trafficConfig = config.traffic_config || {};
                if (trafficConfig.type === 'predefined') {
                    document.getElementById('traffic-preset').checked = true;
                } else {
                    document.getElementById('traffic-custom').checked = true;
                }

                // 应用车辆类型
                const vehicleType = trafficConfig.vehicle_type;
                const vehicleSelect = document.getElementById('vehicleType');
                if (vehicleType === '仅一般车辆') {
                    vehicleSelect.value = 'general';
                } else if (vehicleType === '存在贵宾专车') {
                    vehicleSelect.value = 'vip';
                }

                // 应用组织时段
                const scenario = trafficConfig.scenario;
                const timePhaseSelect = document.getElementById('timePhase');
                if (scenario === '进场') {
                    timePhaseSelect.value = 'entrance';
                } else if (scenario === '离场') {
                    timePhaseSelect.value = 'exit';
                }

                // 应用贵宾专车优先通行
                const vipPriority = trafficConfig.vip_priority || {};
                document.getElementById('vipPriority').checked = vipPriority.enabled || false;

                // 应用分析配置
                const analysisConfig = config.analysis_config || {};
                const edgeAnalysis = analysisConfig.edge_analysis || {};
                document.getElementById('edgeAnalysis').checked = edgeAnalysis.enabled || false;
                selectedAnalysisEdges = edgeAnalysis.selected_edges || [];
                window.selectedAnalysisEdges = selectedAnalysisEdges;
                // 显示/隐藏分析详情区域
                const edgeAnalysisDetails = document.getElementById('edgeAnalysisDetails');
                if (edgeAnalysis.enabled) {
                    edgeAnalysisDetails.style.display = 'block';
                    updateSelectionStatus('edgeAnalysis', selectedAnalysisEdges.length);
                } else {
                    edgeAnalysisDetails.style.display = 'none';
                }

                // 更新UI状态
                updateEntranceTypeState();
                updateVipPriorityState();

            } catch (error) {
                console.error('应用方案配置失败:', error);
                showError('应用方案配置时发生错误');
            }
        }

        async deleteScheme(schemeId) {
            if (!confirm('确定要删除这个方案吗？此操作不可撤销。')) {
                return;
            }

            try {
                const response = await fetch(`${this.apiBase}/api/history/delete/${schemeId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('方案删除成功');
                    this.loadHistoryList(); // 刷新历史列表
                } else {
                    showError(`删除失败: ${result.error}`);
                }
            } catch (error) {
                console.error('删除方案失败:', error);
                showError('删除方案时发生错误，请检查网络连接');
            }
        }
    }

    // 创建历史方案管理器实例
    const historyManager = new HistoryManager();

    // 将历史方案管理器暴露到全局作用域，供HTML中的onclick使用
    window.historyManager = historyManager;
});